#!/bin/bash

set -e

echo "Setting up SIV Monorepo development environment with E2E database seeding mechanism..."

# Update system packages
sudo apt-get update

# Install Node.js 20 (LTS)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs docker-compose

# Install flyway
curl -L https://repo1.maven.org/maven2/org/flywaydb/flyway-commandline/11.2.0/flyway-commandline-11.2.0-linux-x64.tar.gz -o flyway.tar.gz \
        && tar -xzf flyway.tar.gz \
        && sudo mv flyway-11.2.0 /opt/flyway \
        && sudo ln -s /opt/flyway/flyway /usr/local/bin/flyway \
        && rm flyway.tar.gz


# Install pnpm globally
sudo npm install -g pnpm@9.1.4
sudo npm install -g turbo@2.5.0

# Install dependencies
echo "Installing dependencies..."
pnpm install --frozen-lockfile

# Install tsx for running TypeScript files
echo "Installing tsx globally..."
sudo npm install -g tsx

# Generate a valid UUID for the property ID (since we can't run the full database setup in this environment)
echo "Generating test property UUID..."
PROPERTY_ID=$(node -e "console.log(require('crypto').randomUUID())")
THEME_ID=$(node -e "console.log(require('crypto').randomUUID())")

echo "✅ Generated Property ID: $PROPERTY_ID"
echo "✅ Generated Theme ID: $THEME_ID"

# Create the seeding script directory if it doesn't exist
mkdir -p applications/siv-e2e-tests/scripts

# Verify the seeding script exists
if [ ! -f "applications/siv-e2e-tests/scripts/seed-e2e-data.ts" ]; then
    echo "✅ Seeding script already created in previous setup"
else
    echo "✅ Seeding script exists"
fi

# Create E2E environment configuration with the generated property ID
echo "Setting up E2E environment configuration..."
cat > applications/siv-e2e-tests/.env.development.local << EOF
# E2E Test Local Configuration
STS_PASSWORD=test-password-123
PROPERTY_ID=$PROPERTY_ID
THEME_ID=$THEME_ID
NGROK_AUTHTOKEN=test-ngrok-token
LOCAL_MODE=true

# Required environment variables for siv-web server
LEAD_ASSIGNMENT_INBOUND_WEBHOOK_URL=http://localhost:5173/api/integrations/highlevel/webhook
DATABASE_USER=postgres
DATABASE_PASSWORD=secret-postgres-password
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=sivdev
INTERNAL_API_SECRET=super-secret-internal-api-secret-for-e2e-tests
FORM_CDN_URL=http://localhost:5173
FORM_EMBED_BASE_URL=http://localhost:5173
CVENT_WEBHOOK_TOKEN=test-cvent-webhook-token
EOF

echo "✅ Created .env.development.local for E2E tests with Property ID: $PROPERTY_ID"

# Create siv-web environment configuration
cat > applications/siv-web/.env.development.local << EOF
# Local development overrides
LEAD_ASSIGNMENT_INBOUND_WEBHOOK_URL=http://localhost:5173/api/integrations/highlevel/webhook
PROPERTY_ID=$PROPERTY_ID
THEME_ID=$THEME_ID
DATABASE_USER=postgres
DATABASE_PASSWORD=secret-postgres-password
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=sivdev
INTERNAL_API_SECRET=super-secret-internal-api-secret-for-e2e-tests
FORM_CDN_URL=http://localhost:5173
FORM_EMBED_BASE_URL=http://localhost:5173
CVENT_WEBHOOK_TOKEN=test-cvent-webhook-token
EOF

echo "✅ Created .env.development.local for siv-web with Property ID: $PROPERTY_ID"

# Install Playwright dependencies
echo "Installing Playwright dependencies..."
sudo apt-get install -y xvfb libnss3 libnspr4 libatk-bridge2.0-0 libdrm2 libxkbcommon0 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2 libatspi2.0-0 libgtk-3-0 libgdk-pixbuf2.0-0 libxcursor1 libxi6 libxtst6 fonts-liberation libappindicator3-1 xdg-utils

cd applications/siv-e2e-tests
npx playwright install chromium
npx playwright install-deps chromium

# Start Xvfb
sudo Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
XVFB_PID=$!

# Set environment variables
export PLAYWRIGHT_HTML_OPEN=never
export DISPLAY=:99
export PROPERTY_ID=$PROPERTY_ID
export THEME_ID=$THEME_ID

cd ../..

# Create a simple test script to verify the property ID is valid
cat > test-property-id.js << EOF
const propertyId = "$PROPERTY_ID";
const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

if (uuidRegex.test(propertyId)) {
    console.log("✅ Property ID is a valid UUID:", propertyId);
    process.exit(0);
} else {
    console.log("❌ Property ID is not a valid UUID:", propertyId);
    process.exit(1);
}
EOF

node test-property-id.js
rm test-property-id.js

echo ""
echo "✅ Development environment setup complete!"
echo ""
echo "Summary:"
echo "- Node.js $(node --version) and pnpm $(pnpm --version) installed"
echo "- All dependencies installed"
echo "- Database seeding script created at applications/siv-e2e-tests/scripts/seed-e2e-data.ts"
echo "- E2E test environment configured with valid Property ID: $PROPERTY_ID"
echo "- MinIO server running for S3 testing (PID: $MINIO_PID)"
echo "- Xvfb running for headless browser testing (PID: $XVFB_PID)"
echo "- Playwright browsers installed"
echo ""
echo "Environment files created:"
echo "- applications/siv-e2e-tests/.env.development.local (with Property ID: $PROPERTY_ID)"
echo "- applications/siv-web/.env.development.local (with Property ID: $PROPERTY_ID)"
echo ""
echo "Next steps:"
echo "1. Start PostgreSQL database: docker-compose up -d db"
echo "2. Run migrations: ./migrate dev"
echo "3. Run seeding script: cd applications/siv-e2e-tests/scripts && tsx seed-e2e-data.ts"
echo "4. Run E2E tests: PLAYWRIGHT_HTML_OPEN=never DISPLAY=:99 pnpm --filter=@siv/e2e-tests test"
echo ""
echo "The seeding script will create:"
echo "- Test Property: E2E Test Property (with UUID format)"
echo "- Test Theme: E2E Test Theme (with UUID format)"

turbo build
docker compose up -d

./migrate all
