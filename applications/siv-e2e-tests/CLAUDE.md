# CLAUDE.md - E2E Tests

## Playwright Best Practices

### Auto-waiting and Retries
- **NEVER use `waitForTimeout`** - This is an anti-pattern in Playwright
- <PERSON>wright automatically polls and retries element interactions
- Use `expect()` with built-in waiting instead of manual waits
- Example:
  ```typescript
  // ❌ Bad
  await page.waitForTimeout(2000);
  await element.click();
  
  // ✅ Good
  await expect(element).toBeVisible();
  await element.click();
  ```

### Selectors
- **Always add `data-testid` attributes** when tests can't find elements reliably
- Prefer data-testid over complex CSS selectors or text-based selectors
- Example:
  ```typescript
  // ❌ Bad
  const button = page.locator('.btn.btn-primary:has-text("Submit")');
  
  // ✅ Good
  const button = page.locator('[data-testid="submit-button"]');
  ```

## Running Tests

- **Always use `PLAYWRIGHT_HTML_OPEN=never`** to prevent HTML reports from auto-opening
- Run specific test by line number: `pnpm exec playwright test file.spec.ts:24`
- Run with extended timeout if needed: `--timeout=120000`
- When running from repo root: `pnpm --filter=siv-e2e-tests exec playwright test`
- When in the e2e tests directory: `pnpm exec playwright test`

## Page Object Pattern

- Use composition over inheritance with specialized page objects
- Each component should have its own page object (e.g., ValidationPanelPageObject, AssignmentSummaryPageObject)
- Keep page objects focused on element interaction, not business logic

## Common Issues and Solutions

### Dialog Handling
- Always check if dialogs are already open before trying to open them
- Handle "Cannot View Assignment Summary" dialog when overlaps exist
- Example:
  ```typescript
  const dialog = page.locator('[role="dialog"]:has-text("Title")');
  const isVisible = await dialog.isVisible({ timeout: 1000 }).catch(() => false);
  if (isVisible) {
    // Handle existing dialog
  }
  ```

### Validation Panel
- The validation panel may not always appear if there are no issues
- Always check visibility before asserting on content
- Handle both visible and non-visible states gracefully

### Test Stability
- Use descriptive console.log statements to track test progress
- Add proper error handling with try-catch blocks where appropriate
- Always close panels/dialogs after use to avoid state pollution

## Multiple Rules Testing

- The system now supports multiple assignment rules per entity (individual or team)
- Tests should verify that multiple rules can be created and displayed correctly
- Use `addSecondAssignmentRule` method to create additional rules
- Verify with `verifyMultipleRulesInTable` to ensure rules are displayed
- Add small waits after rule creation for UI updates if needed

## Test Writing Best Practices

### Deterministic Tests
- **NEVER use if/else statements in tests** - Tests should be deterministic and assert expected behavior
- **No try/catch blocks** - Let tests fail clearly when assertions don't match
- Example:
  ```typescript
  // ❌ Bad
  if (element.isVisible()) {
    expect(element).toContainText('foo');
  } else {
    expect(element).not.toBeVisible();
  }
  
  // ✅ Good
  await expect(element).toBeVisible();
  await expect(element).toContainText('foo');
  ```

### Adding Test Attributes
- **When tests can't find elements reliably, add data attributes to the implementation code**
- Don't create complex workarounds in tests - fix the source
- Example: Added `data-testid` or other specific attributes like `data-criteria` to buttons for reliable targeting

### Dialog Handling Best Practices
- Use `getByRole('dialog')` or `getByRole('alertdialog')` for dialogs
- Don't assume dialogs need time to appear - Playwright waits automatically
- Always verify dialog content before interacting with it

### Avoid Fragile Selectors
- **Never use column indices or positional selectors** - They break when UI changes
- Use semantic selectors: roles, labels, data-testid
- Add data attributes to implementation code when needed