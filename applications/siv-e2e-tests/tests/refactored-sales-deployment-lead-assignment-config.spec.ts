import {test, expect} from '@playwright/test';
import {RefactoredLeadAssignmentConfigurationPageObject} from '../page-objects/refactored-lead-assignment-configuration-page-object';
import {loginAsCustomerUser} from "../utils/test-auth";

/**
 * Refactored E2E test for lead assignment configuration flow
 * Uses component-based page object architecture with integrated test steps
 * Tests the complete workflow from criteria definition to rule validation
 * 
 * @tag e2e - Regular E2E tests that run during development
 */
test.describe('Refactored Lead Assignment Configuration Flow', {
    tag: ['@e2e', '@customer'],
}, () => {
    let configPage: RefactoredLeadAssignmentConfigurationPageObject;

    test.beforeEach(async ({page}) => {
        await loginAsCustomerUser(page.context())

        configPage = new RefactoredLeadAssignmentConfigurationPageObject(page);
        await configPage.navigate();
    });

    test('complete lead assignment configuration workflow with component architecture', async () => {
        test.setTimeout(160_000);

        // Integrated test step: Initial setup
        await test.step('Perform initial setup', async () => {
            const criteriaConfig = configPage.getCriteriaConfiguration();
            
            // Define assignment criteria
            await criteriaConfig.defineAssignmentCriteria({
                geography: true,
                roomCount: true,
                eventType: true,
                industry: true
            });

            // Configure geography regions
            await criteriaConfig.configureGeographyRegions(['north-america', 'apac', 'western-europe']);

            // Configure room count buckets
            await criteriaConfig.configureRoomCountBuckets([
                {name: 'Small Groups', type: 'less', maxValue: '10'},
                {name: 'Medium Groups', type: 'range', minValue: '10', maxValue: '50'},
                {name: 'Large Groups', type: 'greater', minValue: '50'}
            ]);
        });

        // Integrated test step: Create and verify teams
        await test.step('Create and verify teams', async () => {
            const entityTable = configPage.getEntityTable();
            
            // Create teams
            await entityTable.createTeam('Sales Team A');
            await entityTable.createTeam('Sales Team B');

            // Verify teams are displayed correctly in the table
            await entityTable.verifyEntitiesInTable(['Sales Team A', 'Sales Team B'], []);

            // Verify that newly created teams have all criteria unset initially
            await entityTable.verifyUnsetCriteria('Sales Team A', 'team', ['geography', 'roomCount', 'eventType', 'industry']);
            await entityTable.verifyUnsetCriteria('Sales Team B', 'team', ['geography', 'roomCount', 'eventType', 'industry']);
        });

        // Integrated test step: Configure team criteria with overlaps
        await test.step('Configure overlapping team criteria', async () => {
            const criteriaConfig = configPage.getCriteriaConfiguration();
            
            // Configure Sales Team A
            await criteriaConfig.configureTeamCriteria('Sales Team A', {
                geography: ['North America'],
                roomCount: ['Large Groups', 'Medium Groups'],
                eventType: ['Corporate Event', 'Meeting'],
                industry: ['Technology']
            });

            // Configure Sales Team B with overlaps
            await criteriaConfig.configureTeamCriteria('Sales Team B', {
                geography: ['Western Europe', 'North America'], // Overlap with Team A
                roomCount: ['Medium Groups', 'Small Groups'],   // Overlap with Team A
                eventType: ['Meeting'],                         // Overlap with Team A
                industry: ['Finance']
            });

            // Verify that the criteria are correctly displayed in the table
            await criteriaConfig.verifyTeamCriteria('Sales Team A', [
                'North America', 'Large Groups', 'Medium Groups', 'Corporate Event', 'Meeting', 'Technology'
            ]);
            await criteriaConfig.verifyTeamCriteria('Sales Team B', [
                'Western Europe', 'North America', 'Medium Groups', 'Small Groups', 'Meeting', 'Finance'
            ]);

            // Verify all criteria are set for both teams
            const entityTable = configPage.getEntityTable();
            await entityTable.verifySetCriteria('Sales Team A', 'team', ['geography', 'roomCount', 'eventType', 'industry']);
            await entityTable.verifySetCriteria('Sales Team B', 'team', ['geography', 'roomCount', 'eventType', 'industry']);
        });

        // Integrated test step: Remove default individual if present
        await test.step('Remove default individual if present', async () => {
            const entityTable = configPage.getEntityTable();
            await entityTable.removeDefaultIndividualIfPresent('John Smith');
        });

        // Integrated test step: Create team members and verify inheritance
        await test.step('Create team members and verify inheritance', async () => {
            const entityTable = configPage.getEntityTable();
            
            await entityTable.createIndividual({
                firstName: 'Alice',
                lastName: 'Johnson',
                title: 'Sales Rep',
                email: '<EMAIL>',
                phone: '555-0101',
                teamName: 'Sales Team A'
            });

            await entityTable.createIndividual({
                firstName: 'Carol',
                lastName: 'Davis',
                title: 'Senior Sales Rep',
                email: '<EMAIL>',
                phone: '555-0103',
                teamName: 'Sales Team B'
            });

            // Verify that team members inherit their team's criteria
            await entityTable.verifyTeamMemberInheritance('Alice Johnson', 'Sales Team A');
            await entityTable.verifyTeamMemberInheritance('Carol Davis', 'Sales Team B');

            // Verify team members are displayed correctly in the table
            await entityTable.verifyEntitiesInTable([], ['Alice Johnson', 'Carol Davis']);
        });

        // Integrated test step: Create standalone individuals
        await test.step('Create standalone individuals', async () => {
            const entityTable = configPage.getEntityTable();
            
            await entityTable.createIndividual({
                firstName: 'David',
                lastName: 'Wilson',
                title: 'Sales Manager',
                email: '<EMAIL>',
                phone: '555-0002'
            });

            await entityTable.createIndividual({
                firstName: 'Sarah',
                lastName: 'Thompson',
                title: 'Account Manager',
                email: '<EMAIL>',
                phone: '555-0004'
            });

            // Verify all individuals are displayed correctly in the table
            await entityTable.verifyEntitiesInTable([], ['David Wilson', 'Sarah Thompson']);

            // Verify that standalone individuals start with all criteria unset
            await entityTable.verifyUnsetCriteria('David Wilson', 'individual', ['geography', 'roomCount', 'eventType', 'industry']);
            await entityTable.verifyUnsetCriteria('Sarah Thompson', 'individual', ['geography', 'roomCount', 'eventType', 'industry']);
        });

        // Integrated test step: Test edit dialog functionality
        await test.step('Test edit dialog functionality', async () => {
            const entityTable = configPage.getEntityTable();
            
            // Test that clicking on team names opens edit dialogs
            await entityTable.testEditDialogAccess('Sales Team A', 'team');
            await entityTable.testEditDialogAccess('Sales Team B', 'team');

            // Test that clicking on individual names opens edit dialogs
            await entityTable.testEditDialogAccess('Alice Johnson', 'individual');
            await entityTable.testEditDialogAccess('David Wilson', 'individual');
            await entityTable.testEditDialogAccess('Sarah Thompson', 'individual');

            // Test actually editing a team name
            await entityTable.testTeamEdit('Sales Team B', 'Sales Team Beta');

            // Test actually editing an individual
            await entityTable.testIndividualEdit('Alice Johnson', {
                title: 'Senior Sales Representative',
                phone: '555-0111'
            });

            // Verify the changes are reflected in subsequent operations
            await entityTable.verifyEntitiesInTable(['Sales Team A', 'Sales Team Beta'], ['Alice Johnson']);

            // Verify that team member inheritance is updated with the new team name
            await entityTable.verifyTeamMemberInheritance('Carol Davis', 'Sales Team Beta');
        });

        // Integrated test step: Configure individual criteria with overlaps
        await test.step('Configure overlapping individual criteria', async () => {
            const criteriaConfig = configPage.getCriteriaConfiguration();
            
            // Configure David Wilson to overlap with teams
            await criteriaConfig.configureIndividualCriteria('David Wilson', {
                geography: ['North America'],
                roomCount: ['Medium Groups'],
                eventType: ['Meeting'],
                industry: ['Technology']
            });

            // Verify David Wilson criteria
            await criteriaConfig.verifyIndividualCriteria('David Wilson', [
                'North America', 'Medium Groups', 'Meeting', 'Technology'
            ]);

            // Configure Sarah Thompson to overlap with David Wilson
            await criteriaConfig.configureIndividualCriteria('Sarah Thompson', {
                geography: ['North America'],
                roomCount: ['Medium Groups'],
                eventType: ['Meeting'],
                industry: ['Technology']
            });

            // Verify Sarah Thompson criteria
            await criteriaConfig.verifyIndividualCriteria('Sarah Thompson', [
                'North America', 'Medium Groups', 'Meeting', 'Technology'
            ]);

            // Verify that configured criteria are properly set
            const entityTable = configPage.getEntityTable();
            await entityTable.verifySetCriteria('David Wilson', 'individual', ['geography', 'roomCount', 'eventType', 'industry']);
            await entityTable.verifySetCriteria('Sarah Thompson', 'individual', ['geography', 'roomCount', 'eventType', 'industry']);
        });

        // Integrated test step: Create multiple assignment rules for comprehensive validation
        await test.step('Create multiple assignment rules for validation testing', async () => {
            const ruleManagement = configPage.getRuleManagement();
            
            // Create a second overlapping rule for David Wilson
            await ruleManagement.addSecondAssignmentRule('David Wilson', 'individual', {
                geography: ['Western Europe'],                  // Different geography
                roomCount: ['Medium Groups'],                   // Same room count - will overlap with existing rules
                eventType: ['Corporate Event'],                 // Different event type
                industry: ['Technology']                        // Same industry - will overlap
            });

            // Create a second non-overlapping rule for Sarah Thompson
            await ruleManagement.addSecondAssignmentRule('Sarah Thompson', 'individual', {
                geography: ['APAC'],                           // Different geography - no overlap
                roomCount: ['Small Groups'],                   // Different room count - no overlap
                eventType: ['Conference'],                     // Different event type - no overlap
                industry: ['Finance']                          // Different industry - no overlap
            });

            // Teams are now limited to one rule, so we skip adding a second rule for Sales Team A

            // Verify multiple rules are displayed in the table for individuals only
            await ruleManagement.verifyMultipleRulesInTable('David Wilson', 2);
            // Verify content of David's rules
            await ruleManagement.verifyRuleConfiguration('David Wilson', 0, ['North America', 'Medium Groups', 'Meeting', 'Technology']);
            await ruleManagement.verifyRuleConfiguration('David Wilson', 1, ['Western Europe', 'Medium Groups', 'Corporate Event', 'Technology']);
            
            await ruleManagement.verifyMultipleRulesInTable('Sarah Thompson', 2);
            // Verify content of Sarah's rules
            await ruleManagement.verifyRuleConfiguration('Sarah Thompson', 0, ['North America', 'Medium Groups', 'Meeting', 'Technology']);
            await ruleManagement.verifyRuleConfiguration('Sarah Thompson', 1, ['APAC', 'Small Groups', 'Conference', 'Finance']);
            // Sales Team A should still have only 1 rule
            const teamRow = configPage.page.getByRole('row').filter({ hasText: 'Sales Team A' }).first();
            await expect(teamRow.locator('text="North America"')).toBeVisible();
            await expect(teamRow.locator('text="Large Groups"')).toBeVisible();
        });

        // Integrated test step: Validation panel verification
        await test.step('Verify validation panel shows overlaps correctly', async () => {
            // Trigger validation to populate the panel
            const validationPanel = await configPage.validateRules();
            
            // The validation panel should be visible with our multiple rules
            await expect(validationPanel.panel).toBeVisible();
            
            // The panel should show "Conflicting Rules" section (as seen in screenshot)
            const conflictingRulesSection = configPage.page.locator('text="Conflicting Rules"');
            await expect(conflictingRulesSection).toBeVisible();
            
            // Verify specific conflicts are shown (e.g., "Sales Team A (Rule 1) conflicts with David Wilson (Rule 1)")
            const conflictText = configPage.page.locator('text=/conflicts with/i');
            const conflictCount = await conflictText.count();
            expect(conflictCount).toBeGreaterThan(0);
            
            // The panel should show coverage gaps section
            const coverageGapsSection = configPage.page.locator('text="Coverage Gaps"');
            await expect(coverageGapsSection).toBeVisible();
            
            // Close validation panel for next steps
            await validationPanel.close();
        });

        // Integrated test step: Test overlap indicator navigation to validation panel
        await test.step('Test overlap indicator navigation to validation panel', async () => {
            const overlapDetails = configPage.getOverlapDetails();
            const validationPanel = configPage.getValidationPanel();

            // David Wilson should have overlap indicators
            const hasOverlapIndicator = await overlapDetails.hasOverlapIndicator('David Wilson');
            expect(hasOverlapIndicator).toBe(true);
            
            // Click David Wilson's overlap indicator
            const davidRow = configPage.page.getByRole('row').filter({ hasText: 'David Wilson' }).first();
            const davidOverlapIndicator = davidRow.getByTestId('overlap-indicator');
            await davidOverlapIndicator.click();
            
            // Should open validation panel, not overlap details panel
            await expect(validationPanel.panel).toBeVisible();
            await expect(validationPanel.panel).toContainText('Conflicting Rules');
            
            // Verify David Wilson is mentioned in the conflicts
            await expect(validationPanel.panel).toContainText('David Wilson');
            
            // Should show conflicts with teams and other individuals
            const conflictMessages = validationPanel.panel.locator('text=/conflicts with/i');
            const conflictCount = await conflictMessages.count();
            expect(conflictCount).toBeGreaterThan(0);
            
            // Close validation panel
            await validationPanel.close();
        });

        // Integrated test step: Test assignment summary functionality
        await test.step('Test assignment summary functionality', async () => {
            // Click the View Assignment Summary button
            await expect(configPage.viewAssignmentSummaryButton).toBeVisible();
            await configPage.viewAssignmentSummaryButton.click();
            
            // With overlapping rules, we expect to see the "Cannot View Assignment Summary" dialog
            const dialog = await configPage.page.getByRole('alertdialog');
            await expect(dialog).toContainText("Cannot View Assignment Summary")

            // Wait for and verify the dialog is visible
            await expect(dialog).toBeVisible();
            
            // Verify the dialog content
            await expect(dialog).toContainText('overlapping assignment rules');
            
            // Close the dialog
            const closeButton = dialog.locator('button:has-text("Close")');
            await closeButton.click();
            
            // Wait for dialog to close
            await expect(dialog).toBeHidden();
        });

        // Integrated test step: Final validation and save
        await test.step('Final validation and save configuration', async () => {
            // Verify save & activate button state with a more robust check
            const saveButton = configPage.saveActivateButton;
            await expect(saveButton).toBeVisible();
            
            const isDisabled = await saveButton.isDisabled();
            
            // Since we have overlaps, the button should be disabled
            expect(isDisabled).toBe(true);
            
            // Verify all key aspects have been tested
            expect(configPage.getEntityTable()).toBeDefined();
            expect(configPage.getCriteriaConfiguration()).toBeDefined();
            expect(configPage.getRuleManagement()).toBeDefined();
            expect(configPage.getValidationPanel()).toBeDefined();
        });
    });

    test('basic happy path - configure individuals with assignment rules using component architecture', async () => {
        test.setTimeout(60000);

        // Integrated test step: Remove default individual if present
        await test.step('Remove default individual if present', async () => {
            const entityTable = configPage.getEntityTable();
            await entityTable.removeDefaultIndividualIfPresent('John Smith');
        });

        // Integrated test step: Define basic assignment criteria
        await test.step('Define assignment criteria', async () => {
            const criteriaConfig = configPage.getCriteriaConfiguration();
            await criteriaConfig.defineAssignmentCriteria({
                geography: true,
                eventType: true
            });
        });

        // Integrated test step: Set up geography regions
        await test.step('Configure geography regions', async () => {
            const criteriaConfig = configPage.getCriteriaConfiguration();
            await criteriaConfig.configureGeographyRegions(['north-america', 'apac', 'western-europe']);
        });

        // Integrated test step: Add individuals
        await test.step('Add individuals', async () => {
            const entityTable = configPage.getEntityTable();

            await entityTable.createIndividual({
                firstName: 'Emma',
                lastName: 'Williams',
                title: 'Sales Rep',
                email: '<EMAIL>',
                phone: '555-1001'
            });

            await entityTable.createIndividual({
                firstName: 'James',
                lastName: 'Anderson',
                title: 'Sales Manager',
                email: '<EMAIL>',
                phone: '555-1002'
            });

            // Verify individuals are displayed
            await entityTable.verifyEntitiesInTable([], ['Emma Williams', 'James Anderson']);
        });

        // Integrated test step: Configure assignment rules for individuals
        await test.step('Configure assignment rules', async () => {
            const criteriaConfig = configPage.getCriteriaConfiguration();

            // Configure Emma with North America and Corporate Events
            await criteriaConfig.configureIndividualCriteria('Emma Williams', {
                geography: ['North America'],
                eventType: ['Corporate Event']
            });

            // Configure James with APAC and Meetings
            await criteriaConfig.configureIndividualCriteria('James Anderson', {
                geography: ['APAC'],
                eventType: ['Meeting']
            });

            // Verify criteria are displayed correctly
            await criteriaConfig.verifyIndividualCriteria('Emma Williams', ['North America', 'Corporate Event']);
            await criteriaConfig.verifyIndividualCriteria('James Anderson', ['APAC', 'Meeting']);
        });

        // Integrated test step: Create multiple assignment rules for testing
        await test.step('Create multiple assignment rules for validation testing', async () => {
            const ruleManagement = configPage.getRuleManagement();
            
            // Create a second overlapping rule for Emma Williams
            await ruleManagement.addSecondAssignmentRule('Emma Williams', 'individual', {
                geography: ['APAC'],                           // Different geography but will test overlap detection
                eventType: ['Corporate Event']                 // Same event type - potential overlap
            });

            // Create a second non-overlapping rule for James Anderson
            await ruleManagement.addSecondAssignmentRule('James Anderson', 'individual', {
                geography: ['North America'],                  // Different geography - should not overlap with Emma's first rule
                eventType: ['Conference']                      // Different event type - no overlap
            });

            // Verify multiple rules are displayed in the table
            await ruleManagement.verifyMultipleRulesInTable('Emma Williams', 2);
            // Verify content of Emma's rules
            await ruleManagement.verifyRuleConfiguration('Emma Williams', 0, ['North America', 'Corporate Event']);
            await ruleManagement.verifyRuleConfiguration('Emma Williams', 1, ['APAC', 'Corporate Event']);
            
            await ruleManagement.verifyMultipleRulesInTable('James Anderson', 2);
            // Verify content of James's rules
            await ruleManagement.verifyRuleConfiguration('James Anderson', 0, ['APAC', 'Meeting']);
            await ruleManagement.verifyRuleConfiguration('James Anderson', 1, ['North America', 'Conference']);
        });

        // Integrated test step: Test redundancy detection by creating redundant rules
        await test.step('Test redundancy detection with identical rules', async () => {
            const ruleManagement = configPage.getRuleManagement();
            
            // Create a third rule for Emma that's identical to her first rule (redundant)
            await ruleManagement.addSecondAssignmentRule('Emma Williams', 'individual', {
                geography: ['North America'],                  // Same as Rule 1
                eventType: ['Corporate Event']                 // Same as Rule 1 - fully redundant
            });

            // Verify Emma now has 3 rules
            await ruleManagement.verifyMultipleRulesInTable('Emma Williams', 3);
            
            // Check that Emma's row has redundancy state
            const emmaRow = configPage.page.locator(`[data-testid="individual-row-${await getIndividualId('Emma Williams')}"]`);
            await expect(emmaRow).toHaveAttribute('data-state', 'redundant');
            
            // Validate to confirm redundancy detection
            const validationPanel = await configPage.validateRules();
            await expect(validationPanel.panel).toBeVisible();
            
            // Should show redundancy message
            const panelText = await validationPanel.getTextContent(validationPanel.panel);
            expect(panelText).toContain('redundant');
            
            await validationPanel.close();
        });

        // Integrated test step: Make redundant rule unique and verify state changes
        await test.step('Make redundant rule unique and verify state changes', async () => {
            const ruleManagement = configPage.getRuleManagement();
            
            // Edit Emma's third rule (index 2) to make it unique
            await ruleManagement.editRuleCriteria('Emma Williams', 2, {
                geography: { 
                    remove: ['North America'], 
                    add: ['Western Europe'] 
                },
                eventType: { 
                    remove: ['Corporate Event'], 
                    add: ['Conference'] 
                }
            });
            
            // Check that Emma's row no longer has redundancy state
            const emmaId = await getIndividualId('Emma Williams');
            const emmaRow = configPage.page.locator(`[data-testid="individual-row-${emmaId}"]`);
            await expect(emmaRow).toHaveAttribute('data-state', 'normal');
            
            // Validate again to confirm no redundancy
            const validationPanel = await configPage.validateRules();
            await expect(validationPanel.panel).toBeVisible();
            
            // Should not show redundancy message anymore
            const panelText = await validationPanel.getTextContent(validationPanel.panel);
            expect(panelText).not.toContain('redundant');
            
            await validationPanel.close();
        });

        // Helper function to get individual ID from name
        async function getIndividualId(name: string): Promise<string> {
            const row = configPage.page.getByRole('row').filter({ hasText: name }).first();
            const testId = await row.getAttribute('data-testid');
            return testId?.replace('individual-row-', '') || '';
        }

        // Integrated test step: Validation panel verification
        await test.step('Verify validation panel for single rules', async () => {
            // Trigger validation
            const validationPanel = await configPage.validateRules();

            // Since Emma and James have different geographies and event types,
            // there should be no overlaps with single rules
            const expectedValidEntities = ['Emma Williams', 'James Anderson'];
            await validationPanel.verifyValidationPanelNonOverlappingRules(expectedValidEntities);

            // Verify validation completed successfully
            expect(validationPanel).toBeDefined();

            // Close validation panel
            await validationPanel.close();
        });

        // Integrated test step: Assignment summary verification
        await test.step('Verify assignment summary displays rules', async () => {
            // Check if assignment summary is accessible
            const accessResult = await configPage.checkAssignmentSummaryAccess();

            // Since we have non-overlapping rules, button should be enabled
            expect(accessResult.isEnabled).toBe(true);
            
            // Verify we can access assignment summary
            const assignmentSummary = configPage.getAssignmentSummary();
            await assignmentSummary.open(configPage.viewAssignmentSummaryButton);
            await assignmentSummary.verifyBasicContent();
            
            await assignmentSummary.close();

            // Assignment summary verification completed
            expect(accessResult).toBeDefined();
        });

        // Integrated test step: Validate rules and verify coverage gaps
        await test.step('Validate rules and verify coverage gaps', async () => {
            // Trigger validation
            const validationPanel = await configPage.validateRules();
            
            const validationData = await validationPanel.getValidationDataWithCoverageGaps();

            // Validation panel should be visible with coverage gaps
            expect(validationData.isVisible).toBe(true);
            expect(validationData.hasTable).toBe(true);
            
            // Verify coverage gaps exist
            expect(validationData.coverageGaps).toBeDefined();
            expect(validationData.coverageGaps?.gapCount).toBeGreaterThan(0);
            expect(validationData.coverageGaps?.percentage).toBeGreaterThan(0);
            
            // Verify table headers include all active criteria
            await validationPanel.verifyTableHeaders(['#', 'Event Type', 'Geography']);
            
            // Verify that coverage gaps table is displayed
            const coverageGapTable = configPage.getCoverageGapTable();
            await coverageGapTable.verifyTableDisplayed();
            
            // Verify gap rows are present
            const gapRows = validationPanel.panel.locator('[data-testid*="coverage-gap-row"]');
            const gapRowCount = await gapRows.count();
            expect(gapRowCount).toBeGreaterThan(0);
            
            // Verify warning icons are present in gap rows
            const warningIcons = validationPanel.panel.locator('.lucide-triangle-alert');
            const iconCount = await warningIcons.count();
            expect(iconCount).toBeGreaterThan(0);
            
            // The validation panel shows coverage gaps, which is what we expect
            const panelText = await validationPanel.getTextContent(validationPanel.panel);
            expect(panelText).toContain('unassigned lead scenarios');
            
            // Acknowledge the gaps
            await validationPanel.acknowledgeCoverageGaps();
            
            // Verify panel closed after acknowledgment
            const isPanelClosed = !(await validationPanel.isVisible());
            expect(isPanelClosed).toBe(true);
        });

        // Integrated test step: Verify coverage gaps were acknowledged
        await test.step('Verify coverage gap acknowledgment', async () => {
            // After acknowledgment, validation panel should be closed
            const validationPanel = configPage.getValidationPanel();
            const isPanelVisible = await validationPanel.isVisible();
            expect(isPanelVisible).toBe(false);
            
            // Save button should now be enabled since gaps were acknowledged
            const saveButton = configPage.saveActivateButton;
            const isEnabled = await saveButton.isEnabled();
            expect(isEnabled).toBe(true);
        });

        // Integrated test step: Save and activate configuration
        await test.step('Save and activate configuration', async () => {
            const result = await configPage.saveAndActivateWithVerification();

            // Verify save and activate succeeded
            expect(result.success).toBe(true);
            expect(result.message).toMatch(/Rules Successfully Activated!/i);

            // Verify all major components were tested
            expect(configPage.getEntityTable()).toBeDefined();
            expect(configPage.getCriteriaConfiguration()).toBeDefined();
            expect(configPage.getRuleManagement()).toBeDefined();
            expect(configPage.getValidationPanel()).toBeDefined();
            expect(configPage.getAssignmentSummary()).toBeDefined();
            expect(configPage.getCoverageGapTable()).toBeDefined();
        });
    });

    test('multiple rules lifecycle - creation, overlap detection, and deletion', async () => {
        test.setTimeout(60_000);

        // Integrated test step: Minimal setup with two criteria
        await test.step('Define minimal assignment criteria', async () => {
            const criteriaConfig = configPage.getCriteriaConfiguration();
            await criteriaConfig.defineAssignmentCriteria({
                geography: true,
                eventType: true
            });

            // Configure geography regions
            await criteriaConfig.configureGeographyRegions(['north-america', 'apac']);
        });

        // Integrated test step: Create two individuals
        await test.step('Create test individuals', async () => {
            const entityTable = configPage.getEntityTable();
            
            // Remove default if present
            await entityTable.removeDefaultIndividualIfPresent('John Smith');

            await entityTable.createIndividual({
                firstName: 'Person',
                lastName: 'One',
                title: 'Sales Rep',
                email: '<EMAIL>',
                phone: '555-0001'
            });

            await entityTable.createIndividual({
                firstName: 'Person',
                lastName: 'Two',
                title: 'Sales Manager',
                email: '<EMAIL>',
                phone: '555-0002'
            });

            // Verify individuals are displayed
            await entityTable.verifyEntitiesInTable([], ['Person One', 'Person Two']);
        });

        // Integrated test step: Create multiple rules with intentional overlaps
        await test.step('Create multiple rules with overlaps', async () => {
            const criteriaConfig = configPage.getCriteriaConfiguration();
            const ruleManagement = configPage.getRuleManagement();
            
            // Person One: Rule 1
            await criteriaConfig.configureIndividualCriteria('Person One', {
                geography: ['North America'],
                eventType: ['Meeting']
            });
            
            // Person One: Rule 2 (will overlap with Person Two)
            await ruleManagement.addSecondAssignmentRule('Person One', 'individual', {
                geography: ['North America'],  // Same geography
                eventType: ['Corporate Event'] // Different event type
            });
            
            // Person Two: Rule 1 (overlaps with Person One's Rule 2)
            await criteriaConfig.configureIndividualCriteria('Person Two', {
                geography: ['North America'],   // Overlaps with Person One
                eventType: ['Corporate Event']  // Overlaps with Person One Rule 2
            });
            
            // Verify rule counts
            await ruleManagement.verifyMultipleRulesInTable('Person One', 2);
            // Verify content of Person One's rules
            await ruleManagement.verifyRuleConfiguration('Person One', 0, ['North America', 'Meeting']);
            await ruleManagement.verifyRuleConfiguration('Person One', 1, ['North America', 'Corporate Event']);
            
            // Verify Person Two has their rule configured
            const entityTable = configPage.getEntityTable();
            await entityTable.verifySetCriteria('Person Two', 'individual', ['geography', 'eventType']);
        });

        // Integrated test step: Verify overlap detection with multiple rules
        await test.step('Verify overlap detection with multiple rules', async () => {
            const validationPanel = await configPage.validateRules();
            
            // The validation panel should show conflicts
            await expect(validationPanel.panel).toBeVisible();
            await expect(validationPanel.panel).toContainText('Conflicting Rules');
            
            // Should show specific conflict between Person One (Rule 2) and Person Two (Rule 1)
            const conflictText = validationPanel.panel.locator('text=/Person One.*conflicts with.*Person Two/i');
            await expect(conflictText).toBeVisible();
            
            // Check overlap details for Person One
            const overlapDetails = configPage.getOverlapDetails();
            const hasOverlapIndicator = await overlapDetails.hasOverlapIndicator('Person One');
            expect(hasOverlapIndicator).toBe(true);
            
            // Close the validation panel first since it's in the foreground
            await validationPanel.close();
            
            // Now click the overlap indicator and verify it opens validation panel (not overlap details panel)
            const personOneRow = configPage.page.getByRole('row').filter({ hasText: 'Person One' }).first();
            const personOneOverlapIndicator = personOneRow.getByTestId('overlap-indicator');
            await personOneOverlapIndicator.click();
            
            // Validation panel should open again
            await expect(validationPanel.panel).toBeVisible();
            await expect(validationPanel.panel).toContainText('Conflicting Rules');
            
            // Verify the validation panel shows all the conflict details:
            // 1. The conflict section header
            await expect(validationPanel.panel).toContainText('The following rules overlap and may cause confusion in lead assignment');
            
            // 2. The specific conflict message
            await expect(conflictText).toBeVisible();
            await expect(validationPanel.panel).toContainText('Person One (Rule 2) conflicts with Person Two (Rule 1)');
            
            // 3. Both individuals should be mentioned in the conflicts
            await expect(validationPanel.panel).toContainText('Person One');
            await expect(validationPanel.panel).toContainText('Person Two');
            
            // 4. The panel should also show coverage gaps section
            await expect(validationPanel.panel).toContainText('Coverage Gaps');
            
            // Close the validation panel
            await validationPanel.close();
        });

        // Integrated test step: Test assignment summary with multiple rules
        await test.step('Verify assignment summary handles multiple rules', async () => {
            // Click the View Assignment Summary button - it should show error dialog
            await configPage.viewAssignmentSummaryButton.click();
            
            const dialog = configPage.page.getByRole('alertdialog');
            await expect(dialog).toBeVisible();
            await expect(dialog).toContainText('Cannot View Assignment Summary');
            await expect(dialog).toContainText('overlapping assignment rules');
            
            // Close the dialog
            const closeButton = dialog.locator('button:has-text("Close")');
            await closeButton.click();
            await expect(dialog).toBeHidden();
        });

        // Integrated test step: Test rule deletion and UI updates
        await test.step('Test rule deletion and UI updates', async () => {
            const ruleManagement = configPage.getRuleManagement();
            
            // Take a screenshot to see the current state
            await configPage.page.screenshot({ path: 'before-delete.png' });
            
            // Verify Person One still has 2 rules before deletion
            await ruleManagement.verifyMultipleRulesInTable('Person One', 2);
            
            // Delete Person One's Rule 2 (index 1) - the overlapping one
            await ruleManagement.deleteRule('Person One', 1);
            
            // Verify Person One still has criteria set (now from Rule 1 only)
            const entityTable = configPage.getEntityTable();
            await entityTable.verifySetCriteria('Person One', 'individual', ['geography', 'eventType']);
            
            // Validate again - should have no overlaps now
            const validationPanel = await configPage.validateRules();
            await expect(validationPanel.panel).toBeVisible();
            
            // Should NOT show "Conflicting Rules" section anymore
            const conflictSection = validationPanel.panel.locator('text="Conflicting Rules"');
            const hasConflictSection = await conflictSection.count() > 0;
            expect(hasConflictSection).toBe(false);
            
            // Should show "Coverage Gaps" instead
            const coverageGapsSection = validationPanel.panel.locator('text="Coverage Gaps"');
            await expect(coverageGapsSection).toBeVisible();
            
            await validationPanel.close();
            
            // Assignment summary should now be accessible
            const accessResult = await configPage.checkAssignmentSummaryAccess();
            expect(accessResult.isEnabled).toBe(true);
            
            // Now Person One has only one rule, so it shows inline (no separate rule rows)
            // Let's verify that Person One now shows criteria inline instead of "2 rules"
            const personOneRow = configPage.page.getByRole('row').filter({ hasText: 'Person One' }).first();
            await expect(personOneRow.locator('text="North America"')).toBeVisible();
            await expect(personOneRow.locator('text="Meeting"')).toBeVisible();
            
            // Person Two still has one rule
            const personTwoRow = configPage.page.getByRole('row').filter({ hasText: 'Person Two' }).first();
            await expect(personTwoRow.locator('text="North America"')).toBeVisible();
            await expect(personTwoRow.locator('text="Corporate Event"')).toBeVisible();
        });

        // Integrated test step: Verify we can re-add rules after deletion
        await test.step('Verify rules can be re-added after deletion', async () => {
            const criteriaConfig = configPage.getCriteriaConfiguration();
            
            // Re-add a rule for Person One
            await criteriaConfig.configureIndividualCriteria('Person One', {
                geography: ['APAC'],
                eventType: ['Meeting']
            });
            
            // Verify the rule was added
            const entityTable = configPage.getEntityTable();
            await entityTable.verifySetCriteria('Person One', 'individual', ['geography', 'eventType']);
            
            // Verify "No rule" button is gone
            const personOneRow = configPage.page.getByRole('row').filter({ hasText: 'Person One' });
            const noRuleButton = personOneRow.locator('button:has-text("No rule")');
            await expect(noRuleButton).toBeHidden();
        });
    });
});
