import { Page, Locator, expect } from '@playwright/test';
import { BasePageObject } from './base/base-page-object';
import { ValidationPanelPageObject } from './components/validation-panel-page-object';
import { AssignmentSummaryPageObject } from './components/assignment-summary-page-object';
import { CoverageGapTablePageObject } from './components/coverage-gap-table-page-object';
import { RuleConfigurationDialogPageObject } from './components/rule-configuration-dialog-page-object';
import { EntityTablePageObject } from './components/entity-table-page-object';
import { CriteriaConfigurationPageObject } from './components/criteria-configuration-page-object';
import { OverlapDetailsPageObject } from './components/overlap-details-page-object';
import { RuleManagementPageObject } from './components/rule-management-page-object';

/**
 * Refactored Lead Assignment Configuration Page Object
 * Acts as a coordinator using composition over inheritance
 * Returns specialized component page objects for different UI sections
 */
export class RefactoredLeadAssignmentConfigurationPageObject extends BasePageObject {
  // Main navigation elements
  readonly defineAssignmentCriteriaButton: Locator;
  readonly addTeamButton: Locator;
  readonly addIndividualButton: Locator;
  readonly validateRulesButton: Locator;
  readonly viewAssignmentSummaryButton: Locator;
  readonly saveActivateButton: Locator;

  constructor(page: Page) {
    super(page);

    // Initialize main navigation elements
    this.defineAssignmentCriteriaButton = page.getByRole('button', {name: 'Define Assignment Criteria'});
    this.addTeamButton = page.getByTestId('add-team-button');
    this.addIndividualButton = page.getByTestId('add-individual-button');
    this.validateRulesButton = page.getByTestId('check-issues-button');
    this.viewAssignmentSummaryButton = page.getByRole('button', {name: 'View Assignment Summary'});
    this.saveActivateButton = page.getByRole('button', {name: 'Save & Activate'});
  }

  /**
   * Navigate to the lead assignment configuration page
   */
  async navigate() {
    await this.page.goto('/app/sales-deployment-onboarding');
    await this.waitForLoadingComplete();
    await this.verifyElementState(this.page.getByRole('heading', {name: 'Configure Your Lead Assignment Rules'}), 'visible');
  }

  /**
   * Get the entity table component for team/individual operations
   */
  getEntityTable(): EntityTablePageObject {
    return new EntityTablePageObject(this.page);
  }

  /**
   * Get the criteria configuration component
   */
  getCriteriaConfiguration(): CriteriaConfigurationPageObject {
    return new CriteriaConfigurationPageObject(this.page);
  }

  /**
   * Get the overlap details component
   */
  getOverlapDetails(): OverlapDetailsPageObject {
    return new OverlapDetailsPageObject(this.page);
  }

  /**
   * Get the rule management component
   */
  getRuleManagement(): RuleManagementPageObject {
    return new RuleManagementPageObject(this.page);
  }

  /**
   * Get the validation panel component
   */
  getValidationPanel(): ValidationPanelPageObject {
    return new ValidationPanelPageObject(this.page);
  }

  /**
   * Get the assignment summary component
   */
  getAssignmentSummary(): AssignmentSummaryPageObject {
    return new AssignmentSummaryPageObject(this.page);
  }

  /**
   * Get coverage gap table component
   */
  getCoverageGapTable(): CoverageGapTablePageObject {
    const validationPanel = this.getValidationPanel();
    return new CoverageGapTablePageObject(this.page, validationPanel.panel);
  }

  /**
   * Trigger validation and return validation panel
   */
  async validateRules(): Promise<ValidationPanelPageObject> {
    // Skip dialog closing to avoid timeout issues
    // await this.closeOpenDialogs();

    await this.validateRulesButton.click();

    const validationPanel = this.getValidationPanel();

    // Wait for validation panel to appear using Playwright's auto-waiting
    await validationPanel.waitForVisible();

    return validationPanel;
  }

  /**
   * Check assignment summary accessibility
   */
  async checkAssignmentSummaryAccess() {
    const assignmentSummary = this.getAssignmentSummary();
    return await assignmentSummary.checkAccessibility(this.viewAssignmentSummaryButton);
  }

  /**
   * Save and activate configuration with verification
   */
  async saveAndActivateWithVerification(): Promise<{ success: boolean; message: string }> {
    try {
      await this.saveActivateButton.click();

      // Wait for the success dialog to appear
      const successDialog = this.page.getByRole('alertdialog');
      await expect(successDialog).toBeVisible();
      
      // Verify it contains the success message
      const hasSuccessText = await successDialog.locator('text="Rules Successfully Activated!"').isVisible();
      
      if (hasSuccessText) {
        // Success dialog is visible - close it
        const closeButton = successDialog.getByRole('button', { name: 'Close' });
        await closeButton.click();
        await expect(successDialog).toBeHidden();
        return {success: true, message: 'Rules Successfully Activated!'};
      }
      
      // If dialog exists but no success message, something went wrong
      return {success: false, message: 'Dialog appeared but no success message found'};
    } catch (error) {
      return {success: false, message: `Save and activate failed: ${error}`};
    }
  }


}
