import { Page, Locator, expect } from '@playwright/test';

/**
 * Base Page Object class providing common functionality
 * Implements shared patterns for error handling, waiting, and UI interactions
 */
export abstract class BasePageObject {
  readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  /**
   * Wait for element with retry logic and better error handling
   */
  async waitForElement(locator: Locator, options: {
    timeout?: number;
    state?: 'visible' | 'hidden' | 'attached' | 'detached';
    retries?: number;
  } = {}): Promise<void> {
    const { timeout = 5000, state = 'visible', retries = 3 } = options;
    
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        await locator.waitFor({ state, timeout });
        return;
      } catch (error) {
        if (attempt === retries) {
          throw new Error(`Element not ${state} after ${retries} attempts: ${error}`);
        }
        console.log(`Attempt ${attempt} failed, retrying...`);
      }
    }
  }

  /**
   * Check if element exists without throwing
   */
  async elementExists(locator: Locator, timeout: number = 2000): Promise<boolean> {
    try {
      await locator.waitFor({ state: 'visible', timeout });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get text content safely
   */
  async getTextContent(locator: Locator, defaultValue: string = ''): Promise<string> {
    try {
      const text = await locator.textContent();
      return text?.trim() || defaultValue;
    } catch {
      return defaultValue;
    }
  }

  /**
   * Close any open dialogs or panels
   */
  async closeOpenDialogs(): Promise<void> {
    // Try pressing Escape to close dialogs
    await this.page.keyboard.press('Escape');
    
    // Try clicking visible close buttons
    const closeButtons = this.page.locator('button:has-text("Close"), button:has-text("×"), [aria-label*="close"]');
    const count = await closeButtons.count();
    
    for (let i = 0; i < count; i++) {
      const button = closeButtons.nth(i);
      const isVisible = await this.elementExists(button);
      if (isVisible) {
        await button.click();
        break; // Only close one dialog at a time
      }
    }
  }

  /**
   * Wait for loading states to complete
   */
  async waitForLoadingComplete(timeout: number = 10000): Promise<void> {
    // Wait for network idle
    await this.page.waitForLoadState('networkidle', { timeout });
    
    // Wait for any loading spinners to disappear
    const loadingSpinners = this.page.locator('[data-testid*="loading"], .loading, .spinner');
    const spinnerCount = await loadingSpinners.count();
    
    if (spinnerCount > 0) {
      await loadingSpinners.first().waitFor({ state: 'hidden', timeout });
    }
  }

  /**
   * Scroll element into view
   */
  async scrollIntoView(locator: Locator): Promise<void> {
    await locator.scrollIntoViewIfNeeded();
    // Removed waitForTimeout - Playwright handles scroll automatically
  }

  /**
   * Verify element has expected text content
   */
  async verifyTextContent(locator: Locator, expectedText: string | RegExp, options: {
    timeout?: number;
    exact?: boolean;
  } = {}): Promise<void> {
    const { timeout = 5000, exact = false } = options;
    
    if (typeof expectedText === 'string') {
      if (exact) {
        await expect(locator).toHaveText(expectedText, { timeout });
      } else {
        await expect(locator).toContainText(expectedText, { timeout });
      }
    } else {
      await expect(locator).toContainText(expectedText, { timeout });
    }
  }

  /**
   * Verify element is in expected state
   */
  async verifyElementState(locator: Locator, state: 'visible' | 'hidden' | 'enabled' | 'disabled', timeout: number = 5000): Promise<void> {
    switch (state) {
      case 'visible':
        await expect(locator).toBeVisible({ timeout });
        break;
      case 'hidden':
        await expect(locator).not.toBeVisible({ timeout });
        break;
      case 'enabled':
        await expect(locator).toBeEnabled({ timeout });
        break;
      case 'disabled':
        await expect(locator).toBeDisabled({ timeout });
        break;
    }
  }
}
