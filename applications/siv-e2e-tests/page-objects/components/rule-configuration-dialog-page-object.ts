import { Page, Locator, expect } from '@playwright/test';

/**
 * Page object for Rule Configuration Dialogs
 * Handles geography, room count, event type, and industry configuration dialogs
 */
export class RuleConfigurationDialogPageObject {
  readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  /**
   * Configure geography criteria in the dialog
   */
  async configureGeography(geographyValues: string[]) {
    await expect(this.page.getByText('Edit Geography Rule Criteria')).toBeVisible();
    
    for (const geo of geographyValues) {
      if (geo.includes(':')) {
        await this.page.getByTestId(geo).click();
      } else {
        await this.page.getByRole('checkbox', { name: geo }).click();
      }
    }
    
    await this.page.getByRole('button', { name: 'Save' }).click();
    await expect(this.page.getByText('Edit Geography Rule Criteria')).not.toBeVisible();
  }

  /**
   * Configure room count criteria in the dialog
   */
  async configureRoomCount(roomCountValues: string[]) {
    await expect(this.page.getByText('Edit Room Count')).toBeVisible();
    
    for (const room of roomCountValues) {
      await this.page.getByRole('checkbox', { name: room }).click();
    }
    
    await this.page.getByRole('button', { name: 'Save' }).click();
    await expect(this.page.getByText('Edit Room Count')).not.toBeVisible();
  }

  /**
   * Configure event type criteria in the dialog
   */
  async configureEventType(eventTypeValues: string[]) {
    await expect(this.page.getByText('Edit Event Type')).toBeVisible();
    
    for (const event of eventTypeValues) {
      await this.page.getByRole('checkbox', { name: event }).click();
    }
    
    await this.page.getByRole('button', { name: 'Save' }).click();
    await expect(this.page.getByText('Edit Event Type')).not.toBeVisible();
  }

  /**
   * Configure industry criteria in the dialog
   */
  async configureIndustry(industryValues: string[]) {
    await expect(this.page.getByText('Edit Industry')).toBeVisible();
    
    for (const ind of industryValues) {
      await this.page.getByRole('checkbox', { name: ind }).click();
    }
    
    await this.page.getByRole('button', { name: 'Save' }).click();
    await expect(this.page.getByText('Edit Industry')).not.toBeVisible();
  }

  /**
   * Set criteria to "Any" (uncheck all boxes)
   */
  async setCriteriaToAny(criteriaType: string) {
    const dialog = this.page.getByRole('dialog');
    await expect(dialog).toBeVisible();
    
    // Look for and click the explicit "Any" option
    const anyOption = dialog.getByRole('checkbox', { name: 'Any' });
    const anyOptionExists = await anyOption.isVisible().catch(() => false);
    
    if (anyOptionExists) {
      console.log(`Found "Any" checkbox for ${criteriaType}, clicking it`);
      await anyOption.click();
    } else {
      // Fallback: look for "Any" as text or other selectors
      const anyText = dialog.getByText('Any');
      const anyTextExists = await anyText.isVisible().catch(() => false);
      
      if (anyTextExists) {
        console.log(`Found "Any" text for ${criteriaType}, clicking it`);
        await anyText.click();
      } else {
        console.log(`No "Any" option found for ${criteriaType}, unchecking all boxes as fallback`);
        // Fallback: uncheck all boxes
        const checkedBoxes = dialog.locator('[role="checkbox"][data-state="checked"]');
        const checkedCount = await checkedBoxes.count();
        console.log(`Found ${checkedCount} checked boxes for ${criteriaType}`);
        
        for (let i = 0; i < checkedCount; i++) {
          await checkedBoxes.nth(i).click();
        }
      }
    }
    
    // Save the dialog
    await this.page.getByRole('button', { name: 'Save' }).click();
    await this.page.waitForTimeout(500);
  }

  /**
   * Generic method to configure any criteria type
   */
  async configureCriteria(criteriaType: 'geography' | 'roomCount' | 'eventType' | 'industry', values: string[]) {
    switch (criteriaType) {
      case 'geography':
        await this.configureGeography(values);
        break;
      case 'roomCount':
        await this.configureRoomCount(values);
        break;
      case 'eventType':
        await this.configureEventType(values);
        break;
      case 'industry':
        await this.configureIndustry(values);
        break;
      default:
        throw new Error(`Unknown criteria type: ${criteriaType}`);
    }
  }

  /**
   * Find and click the appropriate criteria button for an entity
   */
  async openCriteriaDialog(
    entityRow: Locator,
    criteriaType: string,
    isAdditionalRule: boolean = false
  ): Promise<void> {
    if (isAdditionalRule) {
      // For additional rules, look in the newest rule row
      const ruleRows = this.page.locator('[data-row-type="rule"]');
      const newRuleRow = ruleRows.last();
      const criteriaButton = newRuleRow.locator(`[data-column="${criteriaType}"] button`).first();
      await criteriaButton.click();
    } else {
      // For first rule, look in main entity row
      const noRuleButton = entityRow.locator(`[data-column="${criteriaType}"] button:has-text("No team rule")`);
      const notSetButton = entityRow.locator(`[data-column="${criteriaType}"] button:has-text("Not Set")`);

      const isNoRuleButton = await noRuleButton.isVisible().catch(() => false);
      const isNotSetButton = await notSetButton.isVisible().catch(() => false);

      if (isNoRuleButton) {
        await noRuleButton.click();
      } else if (isNotSetButton) {
        await notSetButton.click();
      } else {
        // Fallback: click any button in the criteria column
        const anyButton = entityRow.locator(`[data-column="${criteriaType}"] button`).first();
        await anyButton.click();
      }
    }
  }
}
        break;
      case 'roomCount':
        await this.configureRoomCount(values);
        break;
      case 'eventType':
        await this.configureEventType(values);
        break;
      case 'industry':
        await this.configureIndustry(values);
        break;
      default:
        throw new Error(`Unknown criteria type: ${criteriaType}`);
    }
  }

  /**
   * Find and click the appropriate criteria button for an entity
   */
  async openCriteriaDialog(
    entityRow: Locator,
    criteriaType: string,
    isAdditionalRule: boolean = false
  ): Promise<void> {
    if (isAdditionalRule) {
      // For additional rules, look in the newest rule row
      const ruleRows = this.page.locator('[data-row-type="rule"]');
      const newRuleRow = ruleRows.last();
      const criteriaButton = newRuleRow.locator(`[data-column="${criteriaType}"] button`).first();
      await criteriaButton.click();
    } else {
      // For first rule, look in main entity row
      // Try different button states
      const noRuleButton = entityRow.locator(`[data-column="${criteriaType}"] button:has-text("No team rule")`);
      const notSetButton = entityRow.locator(`[data-column="${criteriaType}"] button:has-text("Not Set")`);
      
      const isNoRuleButton = await noRuleButton.isVisible().catch(() => false);
      const isNotSetButton = await notSetButton.isVisible().catch(() => false);

      if (isNoRuleButton) {
        await noRuleButton.click();
      } else if (isNotSetButton) {
        await notSetButton.click();
      } else {
        // Fallback: click any button in the criteria column
        const anyButton = entityRow.locator(`[data-column="${criteriaType}"] button`).first();
        await anyButton.click();
      }
    }
  }
}
