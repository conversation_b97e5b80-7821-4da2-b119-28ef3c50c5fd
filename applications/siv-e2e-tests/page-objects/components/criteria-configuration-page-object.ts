import { Page, Locator, expect } from '@playwright/test';
import { BasePageObject } from '../base/base-page-object';

/**
 * Criteria Configuration Page Object
 * Handles all operations related to defining and configuring assignment criteria
 */
export class CriteriaConfigurationPageObject extends BasePageObject {
  // Main criteria definition
  readonly defineAssignmentCriteriaButton: Locator;

  // Geography management
  readonly geographyHeaderButton: Locator;
  readonly quickAddNorthAmericaButton: Locator;
  readonly quickAddApacButton: Locator;
  readonly quickAddWesternEuropeButton: Locator;
  readonly userDefinedRegions: Locator;
  readonly footerSaveAllChangesButton: Locator;

  // Room count management
  readonly roomCountHeaderButton: Locator;
  readonly bucketNameInput: Locator;
  readonly conditionTypeSelect: Locator;
  readonly minValueInput: Locator;
  readonly maxValueInput: Locator;
  readonly saveBucketButton: Locator;
  readonly saveAllRoomCountBucketsButton: Locator;

  constructor(page: Page) {
    super(page);
    
    this.defineAssignmentCriteriaButton = page.getByRole('button', { name: 'Define Assignment Criteria' });
    
    // Geography management elements
    this.geographyHeaderButton = page.getByTestId('geography-header').getByRole('button');
    this.quickAddNorthAmericaButton = page.getByTestId('quick-add-template-north-america');
    this.quickAddApacButton = page.getByTestId('quick-add-template-apac');
    this.quickAddWesternEuropeButton = page.getByTestId('quick-add-template-western-europe');
    this.userDefinedRegions = page.getByTestId('user-defined-regions');
    this.footerSaveAllChangesButton = page.getByTestId('footer-save-all-changes-button');

    // Room count management elements
    this.roomCountHeaderButton = page.getByTestId('roomCount-header').getByRole('button');
    this.bucketNameInput = page.getByTestId('bucket-name-input');
    this.conditionTypeSelect = page.getByTestId('condition-type');
    this.minValueInput = page.getByTestId('min-value-input');
    this.maxValueInput = page.getByTestId('max-value-input');
    this.saveBucketButton = page.getByTestId('save-bucket');
    this.saveAllRoomCountBucketsButton = page.getByTestId('save-all-roomcount-buckets');
  }

  /**
   * Define assignment criteria by selecting checkboxes
   */
  async defineAssignmentCriteria(criteria: {
    geography?: boolean;
    roomCount?: boolean;
    eventType?: boolean;
    industry?: boolean;
  }) {
    await this.waitForElement(this.defineAssignmentCriteriaButton);
    await this.defineAssignmentCriteriaButton.click();

    const dialog = this.page.getByRole('dialog');
    await this.waitForElement(dialog);
    await this.verifyTextContent(dialog.getByRole('heading'), 'Select Your Assignment Factors');

    if (criteria.geography) {
      await this.page.getByTestId('geography-checkbox').click();
      await expect(this.page.getByTestId('geography-checkbox')).toHaveAttribute('data-state', 'checked');
    }
    if (criteria.roomCount) {
      await this.page.getByTestId('roomCount-checkbox').click();
      await expect(this.page.getByTestId('roomCount-checkbox')).toHaveAttribute('data-state', 'checked');
    }
    if (criteria.eventType) {
      await this.page.getByTestId('eventType-checkbox').click();
      await expect(this.page.getByTestId('eventType-checkbox')).toHaveAttribute('data-state', 'checked');
    }
    if (criteria.industry) {
      await this.page.getByTestId('industry-checkbox').click();
      await expect(this.page.getByTestId('industry-checkbox')).toHaveAttribute('data-state', 'checked');
    }

    await this.page.getByTestId('save-criteria-button').click();
    await this.verifyElementState(dialog, 'hidden');
  }

  /**
   * Configure geography regions using quick-add templates
   */
  async configureGeographyRegions(regions: string[]) {
    await this.geographyHeaderButton.click();
    
    for (const region of regions) {
      switch (region) {
        case 'north-america':
          await this.quickAddNorthAmericaButton.click();
          break;
        case 'apac':
          await this.quickAddApacButton.click();
          break;
        case 'western-europe':
          await this.quickAddWesternEuropeButton.click();
          break;
      }
    }

    // Verify regions are added
    for (const region of regions) {
      const regionName = region === 'north-america' ? 'North America' : 
                        region === 'apac' ? 'APAC' : 'Western Europe';
      await this.verifyTextContent(this.userDefinedRegions.getByText(regionName), regionName);
    }

    await this.footerSaveAllChangesButton.click();
  }

  /**
   * Configure room count buckets
   */
  async configureRoomCountBuckets(buckets: Array<{
    name: string;
    type: 'less' | 'range' | 'greater';
    minValue?: string;
    maxValue?: string;
  }>) {
    await this.roomCountHeaderButton.click();
    const dialog = this.page.getByRole('dialog');
    await this.waitForElement(dialog);

    for (const bucket of buckets) {
      await this.bucketNameInput.fill(bucket.name);
      await this.conditionTypeSelect.click();
      await this.page.getByTestId(bucket.type).click();
      
      if (bucket.minValue) {
        await this.minValueInput.fill(bucket.minValue);
      }
      if (bucket.maxValue) {
        await this.maxValueInput.fill(bucket.maxValue);
      }
      
      await this.saveBucketButton.click();
    }

    await this.saveAllRoomCountBucketsButton.click();
    await this.verifyElementState(dialog, 'hidden');
  }

  /**
   * Configure criteria for a team - configures the first existing rule
   */
  async configureTeamCriteria(teamName: string, criteria: {
    geography?: string[];
    roomCount?: string[];
    eventType?: string[];
    industry?: string[];
  }) {
    // Find the team row
    const teamRow = this.page.getByRole('row').filter({ hasText: teamName }).first();

    // Get the team ID from the row's data-testid
    const teamRowTestId = await teamRow.getAttribute('data-testid');
    const teamId = teamRowTestId?.replace('team-row-', '') || '';

    // Configure the criteria for the existing first rule (entities now start with one empty rule)
    await this.configureCriteriaForEntity(teamId, 'team', criteria);
  }

  /**
   * Configure criteria for an individual - configures the first existing rule
   */
  async configureIndividualCriteria(individualIdentifier: string, criteria: {
    geography?: string[];
    roomCount?: string[];
    eventType?: string[];
    industry?: string[];
  }) {
    let individualRow: Locator;
    let individualId: string;

    // Handle both testid and text-based identification
    if (individualIdentifier.startsWith('individual-row-')) {
      individualRow = this.page.getByTestId(individualIdentifier);
      individualId = individualIdentifier.replace('individual-row-', '');
    } else {
      individualRow = this.page.getByRole('row').filter({ hasText: individualIdentifier }).first();
      // Get the individual ID from the row's data-testid
      const rowTestId = await individualRow.getAttribute('data-testid');
      individualId = rowTestId?.replace('individual-row-', '') || '';
    }

    // Configure the criteria for the existing first rule (entities now start with one empty rule)
    await this.configureCriteriaForEntity(individualId, 'individual', criteria);
  }

  /**
   * Configure criteria for an entity (team or individual)
   */
  private async configureCriteriaForEntity(entityId: string, entityType: 'team' | 'individual', criteria: {
    geography?: string[];
    roomCount?: string[];
    eventType?: string[];
    industry?: string[];
  }) {
    const entityRowElement = this.page.getByTestId(`${entityType}-row-${entityId}`);

    // Configure geography if provided
    if (criteria.geography) {
      await this.configureCriteriaType(entityRowElement, 'geography', criteria.geography, 'Edit Geography Rule Criteria');
    }

    if (criteria.roomCount) {
      await this.configureCriteriaType(entityRowElement, 'roomCount', criteria.roomCount, 'Edit Room Count');
    }

    if (criteria.eventType) {
      await this.configureCriteriaType(entityRowElement, 'eventType', criteria.eventType, 'Edit Event Type');
    }

    if (criteria.industry) {
      await this.configureCriteriaType(entityRowElement, 'industry', criteria.industry, 'Edit Industry');
    }
  }

  /**
   * Configure a specific criteria type
   */
  private async configureCriteriaType(entityRow: Locator, criteriaType: string, values: string[], dialogTitle: string) {
    console.log(`Configuring ${criteriaType} with values: ${values.join(', ')}`);
    
    // Try to find "No team rule" button first, then "Not Set" button
    const noRuleButton = entityRow.locator(`[data-column="${criteriaType}"] button:has-text("No team rule")`);
    const notSetButton = entityRow.locator(`[data-column="${criteriaType}"] button:has-text("Not Set")`);
    
    const hasNoRuleButton = await this.elementExists(noRuleButton);
    const hasNotSetButton = await this.elementExists(notSetButton);

    console.log(`Looking for criteria button - hasNoRuleButton: ${hasNoRuleButton}, hasNotSetButton: ${hasNotSetButton}`);

    if (hasNoRuleButton) {
      await noRuleButton.click();
    } else if (hasNotSetButton) {
      await notSetButton.click();
    } else {
      // Look for it in rule rows
      console.log(`Looking in rule rows for ${criteriaType} button`);
      const ruleRows = entityRow.locator('[data-row-type="rule"]');
      const ruleRowCount = await ruleRows.count();
      console.log(`Found ${ruleRowCount} rule rows`);
      
      if (ruleRowCount > 0) {
        const firstRuleRow = ruleRows.first();
        const button = firstRuleRow.locator(`[data-column="${criteriaType}"] button`).first();
        await button.click();
      } else {
        // Try finding the button directly in the entity row
        const button = entityRow.locator(`[data-column="${criteriaType}"] button`).first();
        await button.click();
      }
    }

    // Wait for the criteria dialog to be visible
    await this.verifyTextContent(this.page.getByText(dialogTitle), dialogTitle);

    for (const value of values) {
      console.log(`Selecting criteria value: ${value}`);
      if (value.includes(':')) {
        await this.page.getByTestId(value).click();
      } else {
        await this.page.getByRole('checkbox', { name: value }).click();
      }
    }

    await this.page.getByRole('button', { name: 'Save' }).click();
    await expect(this.page.getByText(dialogTitle)).not.toBeVisible();
    console.log(`Successfully configured ${criteriaType}`);
  }

  /**
   * Verify team criteria are displayed correctly
   */
  async verifyTeamCriteria(teamName: string, expectedCriteria: string[]) {
    await this.verifyCriteriaDisplay(teamName, expectedCriteria);
  }

  /**
   * Verify individual criteria are displayed correctly
   */
  async verifyIndividualCriteria(individualIdentifier: string, expectedCriteria: string[]) {
    await this.verifyCriteriaDisplay(individualIdentifier, expectedCriteria);
  }

  /**
   * Verify criteria are displayed correctly for an entity
   */
  private async verifyCriteriaDisplay(entityIdentifier: string, expectedCriteria: string[]) {
    for (const criteria of expectedCriteria) {
      // Look for the criteria text anywhere in the table
      const criteriaText = this.page.getByText(criteria);
      const isVisibleAnywhere = await this.elementExists(criteriaText);

      if (isVisibleAnywhere) {
        await this.verifyElementState(criteriaText.first(), 'visible');
      } else {
        console.log(`Warning: Could not find exact criteria text "${criteria}" for entity ${entityIdentifier}`);
      }
    }
  }

  /**
   * Clear all criteria for an individual (remove all rules)
   */
  async clearIndividualCriteria(individualIdentifier: string) {
    let individualId: string;

    if (individualIdentifier.startsWith('individual-row-')) {
      individualId = individualIdentifier.replace('individual-row-', '');
    } else {
      const individualRow = this.page.getByRole('row').filter({ hasText: individualIdentifier }).first();
      const rowTestId = await individualRow.getAttribute('data-testid');
      individualId = rowTestId?.replace('individual-row-', '') || '';
    }

    // Delete all existing rules for this individual
    let hasMoreRules = true;
    let attempts = 0;
    const maxAttempts = 10;

    while (hasMoreRules && attempts < maxAttempts) {
      attempts++;

      // Look for delete buttons
      const deleteButtons = this.page.locator('button[aria-label*="Delete"], button:has-text("Delete"), button[title*="delete"]');
      const deleteButtonCount = await deleteButtons.count();

      if (deleteButtonCount === 0) {
        hasMoreRules = false;
        break;
      }

      // Try to find a delete button that's visible and clickable
      let foundClickableButton = false;
      for (let i = 0; i < deleteButtonCount; i++) {
        const deleteButton = deleteButtons.nth(i);
        const isVisible = await this.elementExists(deleteButton);

        if (isVisible) {
          await deleteButton.click();
          foundClickableButton = true;

          // Wait for any confirmation dialog and confirm
          const confirmButton = this.page.getByRole('button', { name: /confirm|delete|yes/i });
          const confirmExists = await this.elementExists(confirmButton);
          if (confirmExists) {
            await confirmButton.click();
          }

          // Wait for the rule to be deleted
          await this.page.waitForTimeout(1000);
          break;
        }
      }

      if (!foundClickableButton) {
        hasMoreRules = false;
      }
    }
  }
}
