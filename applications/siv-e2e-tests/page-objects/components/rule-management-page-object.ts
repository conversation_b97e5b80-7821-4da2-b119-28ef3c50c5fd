import { Page, Locator, expect } from '@playwright/test';
import { BasePageObject } from '../base/base-page-object';

/**
 * Rule Management Page Object - FIXED VERSION
 * Handles operations related to adding, editing, and managing multiple assignment rules
 * Based on screenshot analysis showing multiple rules working correctly
 */
export class RuleManagementPageObject extends BasePageObject {

  constructor(page: Page) {
    super(page);
  }

  /**
   * Add a second assignment rule for an entity - SIMPLIFIED VERSION
   */
  async addSecondAssignmentRule(entityName: string, entityType: 'individual' | 'team', criteria: {
    geography?: string[];
    roomCount?: string[];
    eventType?: string[];
    industry?: string[];
  }) {
    console.log(`\nAttempting to add additional rule for ${entityName} (${entityType})`);
    
    // Find the entity row
    const entityRow = this.page.getByRole('row').filter({ hasText: entityName }).first();
    await expect(entityRow).toBeVisible({ timeout: 5000 });

    // Count existing rules first
    const ruleCountIndicator = entityRow.locator('td').filter({ hasText: /\d+ rules?/i }).first();
    let currentRuleCount = 1;
    if (await ruleCountIndicator.isVisible()) {
      const ruleCountText = await ruleCountIndicator.textContent();
      const match = ruleCountText?.match(/(\d+) rules?/i);
      currentRuleCount = match ? parseInt(match[1], 10) : 1;
    }
    const nextRuleNumber = currentRuleCount + 1;

    // Look for "Add Rule" button
    const addRuleButton = entityRow.locator('button').filter({ hasText: /add rule/i }).first();
    await expect(addRuleButton).toBeVisible();
    console.log(`Found add rule button for ${entityName}, clicking...`);
    await addRuleButton.click();
    
    // Handle the add rule dialog
    const dialog = this.page.getByRole('alertdialog');
    await expect(dialog).toBeVisible();
    
    // Find and click the "Add Rule" button using the data-testid
    const confirmAddRuleButton = dialog.locator('[data-testid="confirmation-confirm-button"]');
    await expect(confirmAddRuleButton).toBeVisible();
    await confirmAddRuleButton.click();
    
    // Wait for dialog to close
    await expect(dialog).toBeHidden();
    
    // Now configure the criteria for the new rule
    console.log(`Rule ${nextRuleNumber} added successfully for ${entityName}, now configuring criteria...`);
    await this.configureCriteriaForNewRule(entityName, entityType, criteria, nextRuleNumber);
    console.log(`Rule ${nextRuleNumber} configuration completed for ${entityName}`);
  }

  /**
   * Configure criteria for a newly added rule - FIXED VERSION
   */
  private async configureCriteriaForNewRule(entityName: string, entityType: 'individual' | 'team', criteria: {
    geography?: string[];
    roomCount?: string[];
    eventType?: string[];
    industry?: string[];
  }, ruleNumber: number) {
    // Look for the specific rule number
    const newRuleRow = this.page.getByRole('row').filter({ hasText: `Rule ${ruleNumber}` }).last();
    
    console.log(`Looking for Rule ${ruleNumber} row for ${entityName}`);
    await expect(newRuleRow).toBeVisible();
    
    // Configure in the order they appear in the table: geography, roomCount, eventType, industry
    if (criteria.geography) {
      await this.configureCriteriaTypeForRule(newRuleRow, 'geography', criteria.geography);
    }

    if (criteria.roomCount) {
      await this.configureCriteriaTypeForRule(newRuleRow, 'roomCount', criteria.roomCount);
    }

    if (criteria.eventType) {
      await this.configureCriteriaTypeForRule(newRuleRow, 'eventType', criteria.eventType);
    }

    if (criteria.industry) {
      await this.configureCriteriaTypeForRule(newRuleRow, 'industry', criteria.industry);
    }
  }

  /**
   * Configure a specific criteria type for a rule - DETERMINISTIC VERSION
   */
  private async configureCriteriaTypeForRule(ruleRow: Locator, criteriaType: string, values: string[]) {
    // Look for button with data-criteria attribute
    const criteriaButton = ruleRow.locator(`button[data-criteria="${criteriaType}"]`);
    await expect(criteriaButton).toBeVisible();
    await criteriaButton.click();

    // Wait for dialog to appear
    const dialog = this.page.getByRole('dialog');
    await expect(dialog).toBeVisible();

    // Select the provided values
    for (const value of values) {
      // Look for the checkbox or clickable element with the value text
      const valueOption = dialog.getByText(value).first();
      await valueOption.click();
    }

    // Save the changes
    const saveButton = dialog.getByRole('button', { name: /save/i });
    await expect(saveButton).toBeVisible();
    await saveButton.click();
    
    // Wait for dialog to close
    await expect(dialog).toBeHidden();
  }

  /**
   * Verify multiple rules are displayed in the table - DETERMINISTIC VERSION
   */
  async verifyMultipleRulesInTable(entityName: string, expectedRuleCount: number) {
    // Find the entity row - it must exist
    const entityRow = this.page.getByRole('row').filter({ hasText: entityName }).first();
    
    // Scroll into view and verify visibility
    await entityRow.scrollIntoViewIfNeeded();
    await expect(entityRow).toBeVisible();
    
    // Find the rule count indicator in table cells (e.g., "2 rules")
    const ruleCountIndicator = entityRow.locator('td').filter({ hasText: /\d+ rules?/i }).first();
    
    // The rule count indicator must exist
    await expect(ruleCountIndicator).toBeVisible();
    
    // Extract and verify the count
    const ruleCountText = await ruleCountIndicator.textContent();
    const match = ruleCountText?.match(/(\d+) rules?/i);
    const actualCount = match ? parseInt(match[1], 10) : 0;
    
    // Assert the exact count
    expect(actualCount).toBe(expectedRuleCount);
  }

  /**
   * Delete a specific rule for an entity - DETERMINISTIC VERSION
   */
  async deleteRule(entityName: string, ruleIndex: number = 0) {
    console.log(`Attempting to delete rule ${ruleIndex} for ${entityName}`);
    
    // Find the entity row first
    const entityRow = this.page.getByRole('row').filter({ hasText: entityName }).first();
    await expect(entityRow).toBeVisible();
    
    // Get the entity ID from the data-testid attribute
    const entityTestId = await entityRow.getAttribute('data-testid');
    const entityId = entityTestId?.replace(/^(team|individual)-row-/, '');
    
    if (!entityId) {
      throw new Error(`Could not find entity ID for ${entityName}`);
    }
    
    console.log(`Found entity ID: ${entityId}`);
    
    // Find all delete rule buttons for this entity using data-testid pattern
    const deleteButtons = this.page.locator(`[data-testid^="delete-rule-${entityId}-"]`);
    const deleteButtonCount = await deleteButtons.count();
    console.log(`Found ${deleteButtonCount} delete buttons for ${entityName}`);
    
    // Click the delete button at the specified index
    const deleteButton = deleteButtons.nth(ruleIndex);
    await expect(deleteButton).toBeVisible();
    await deleteButton.click();

    // No confirmation dialog for rule deletion - the rule is deleted immediately
    // Wait for the delete button to be detached from DOM (indicates deletion completed)
    await expect(deleteButton).toBeHidden();
    
    console.log(`Successfully deleted rule ${ruleIndex} for ${entityName}`);
  }

  /**
   * Verify rule configuration is displayed correctly - FIXED VERSION
   */
  async verifyRuleConfiguration(entityName: string, ruleIndex: number, expectedCriteria: string[]) {
    try {
      if (ruleIndex === 0) {
        // First rule - use the main entity row
        const entityRow = this.page.getByRole('row').filter({ hasText: entityName }).first();
        await this.verifyRuleCriteriaInRow(entityRow, expectedCriteria);
      } else {
        // Additional rules - look for Rule 2, Rule 3, etc.
        // The rule rows come immediately after the entity row
        const entityRow = this.page.getByRole('row').filter({ hasText: entityName }).first();
        const ruleLabel = `Rule ${ruleIndex + 1}`;
        
        // Find the rule row by looking for the next row after the entity that contains the rule label
        // Rule rows don't contain the entity name, they're just subsequent rows
        const allRows = this.page.getByRole('row');
        const entityRowIndex = await allRows.all().then(async (rows) => {
          for (let i = 0; i < rows.length; i++) {
            const text = await rows[i].textContent();
            if (text?.includes(entityName)) {
              return i;
            }
          }
          return -1;
        });
        
        if (entityRowIndex >= 0) {
          // Look for the rule row in the next few rows after the entity
          const ruleRow = allRows.nth(entityRowIndex + ruleIndex);
          const ruleRowText = await ruleRow.textContent();
          
          if (ruleRowText?.includes(ruleLabel)) {
            await this.verifyRuleCriteriaInRow(ruleRow, expectedCriteria);
          } else {
            console.log(`Warning: Could not find ${ruleLabel} for ${entityName}`);
          }
        } else {
          console.log(`Warning: Could not find entity row for ${entityName}`);
        }
      }
    } catch (error) {
      console.log(`Warning: Failed to verify rule configuration for ${entityName}: ${(error as any).message}`);
    }
  }

  /**
   * Helper method to verify criteria in a rule row
   */
  private async verifyRuleCriteriaInRow(ruleRow: Locator, expectedCriteria: string[]) {
    for (const criteria of expectedCriteria) {
      const criteriaText = ruleRow.getByText(criteria);
      const isVisible = await this.elementExists(criteriaText);
      
      if (isVisible) {
        console.log(`✓ Found criteria "${criteria}" in rule row`);
      } else {
        console.log(`Warning: Could not find criteria "${criteria}" in rule row`);
      }
    }
  }

  /**
   * Edit criteria for a specific rule by index
   */
  async editRuleCriteria(individualName: string, ruleIndex: number, criteria: {
    geography?: { add?: string[], remove?: string[] };
    eventType?: { add?: string[], remove?: string[] };
    roomCount?: { add?: string[], remove?: string[] };
    industry?: { add?: string[], remove?: string[] };
  }) {
    // Find the individual row first to get the ID
    const individualRow = this.page.getByRole('row').filter({ hasText: individualName }).first();
    await expect(individualRow).toBeVisible();
    
    const individualTestId = await individualRow.getAttribute('data-testid');
    const individualId = individualTestId?.replace('individual-row-', '') || '';
    
    if (!individualId) {
      throw new Error(`Could not find individual ID for ${individualName}`);
    }
    
    // Find the specific rule row by data attributes
    const ruleRow = this.page.locator(`[data-row-type="rule"][data-individual-id="${individualId}"][data-rule-index="${ruleIndex}"]`);
    await expect(ruleRow).toBeVisible();
    
    // Edit each criteria type if provided
    for (const [criteriaType, changes] of Object.entries(criteria)) {
      if (!changes) continue;
      
      // Click the button for this criteria type
      const criteriaButton = ruleRow.locator(`[data-column="${criteriaType}"] button`).first();
      await criteriaButton.click();
      
      // Wait for dialog
      const dialog = this.page.getByRole('dialog');
      await expect(dialog).toBeVisible();
      
      // Remove selections
      if (changes.remove) {
        for (const value of changes.remove) {
          await dialog.getByText(value).click();
        }
      }
      
      // Add selections
      if (changes.add) {
        for (const value of changes.add) {
          await dialog.getByText(value).click();
        }
      }
      
      // Save changes
      const saveButton = dialog.getByRole('button', { name: /save/i });
      await saveButton.click();
      await expect(dialog).toBeHidden();
    }
  }
}
