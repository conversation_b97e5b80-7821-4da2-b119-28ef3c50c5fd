import { Locator, Page, expect } from '@playwright/test';

export class StsPage {
  readonly page: Page;
  readonly eventInfoPanel: Locator;
  readonly eventTable: Locator;
  readonly tableRows: Locator;

  constructor(page: Page) {
    this.page = page;
    this.eventInfoPanel = page.locator('div.panel:has(.panel-heading:has-text("Event Information"))');
    this.eventTable = this.eventInfoPanel.locator('table.table-bordered');
    this.tableRows = this.eventTable.locator('tr');
  }

  async login(username: string, password: string) {
    await this.page.goto('https://proposalpath.com/login');
    await this.page.fill('input[name="username"]', username);
    await this.page.fill('input[name="password"]', password);
    await this.page.click('input[type="submit"][value="Sign in"]');
  }

  async navigateToLeads() {
    await this.page.locator('text=Leads').click();
  }

  async findLeadByName(firstName: string, lastName: string) {
    // Wait for the table to be fully loaded
    await this.page.waitForSelector('tr[data-action="click->leads-dashboard#leadClicked"]', { state: 'visible', timeout: 15000 });
    
    // Create the full name
    const fullName = `${firstName} ${lastName}`;
    
    // Get all rows with the first name
    const allLeadRows = this.page.locator(`tr[data-action="click->leads-dashboard#leadClicked"] td:nth-child(7):has-text("${firstName}")`);
    const count = await allLeadRows.count();
    
    if (count === 0) {
      throw new Error(`No leads found with first name "${firstName}"`);
    } else if (count === 1) {
      // Only one match, use it
      console.log(`Found exactly one lead with first name "${firstName}"`);
      await expect(allLeadRows).toBeVisible({ timeout: 20000 });
      return allLeadRows.locator('..').first();
    } else {
      // Multiple matches, find the one containing the full name
      console.log(`Found ${count} leads with first name "${firstName}", searching for one containing "${fullName}"`);
      
      // Evaluate all matches to find the one containing the full name
      for (let i = 0; i < count; i++) {
        const leadText = await allLeadRows.nth(i).textContent();
        console.log(`Lead ${i+1} text: "${leadText}"`);
        
        if (leadText && leadText.includes(lastName)) {
          console.log(`Found lead with full name "${fullName}" at index ${i}`);
          const matchingRow = allLeadRows.nth(i);
          await expect(matchingRow).toBeVisible({ timeout: 20000 });
          return matchingRow.locator('..').first();
        }
      }
      
      // If we get here, we didn't find a match with the full name
      console.log(`No lead found containing last name "${lastName}", using first match as fallback`);
      await expect(allLeadRows.first()).toBeVisible({ timeout: 20000 });
      return allLeadRows.first().locator('..').first();
    }
  }
  
  async clickOnLead(leadRow: Locator) {
    await leadRow.click();
    // Wait for the lead details page to load - using a more reliable selector
    await this.page.waitForSelector('a[href^="/leads/"]', { state: 'visible' });
  }

  async verifyContactInformation(firstName: string, lastName: string, email: string, company: string, phone: string) {
    // Verify contact name
    await expect(
      this.page.locator('div.panel:has(.panel-heading:has-text("Contact Information")) .table tr:has(td:has-text("Contact Name")) td:nth-child(2)')
    ).toContainText(`${firstName} ${lastName}`);
    
    // Verify email
    await expect(
      this.page.locator('div.panel:has(.panel-heading:has-text("Contact Information")) .table tr:has(td:has-text("Email")) td:nth-child(2) a')
    ).toHaveText(email);
    
    // Verify company
    await expect(
      this.page.locator('div.panel:has(.panel-heading:has-text("Contact Information")) .table tr:has(td:has-text("Company")) td:nth-child(2)')
    ).toHaveText(company);
    
    // Verify phone
    const phoneElement = this.page.locator('div.panel:has(.panel-heading:has-text("Contact Information")) .table tr:has(td:has-text("Phone")) td:nth-child(2)');
    const phoneText = await phoneElement.textContent() || '';
    expect(phoneText).toContain(phone.replace(/\D/g, ''));
  }

  async navigateToLeadDetails(leadId: string) {
    await this.page.goto(`/leads/${leadId}`);
    await expect(this.eventInfoPanel).toBeVisible();
    await expect(this.eventTable).toBeVisible({ timeout: 5000 });
  }

  async getRowCount() {
    return await this.tableRows.count();
  }

  async findRowByHeaderText(headerText: string) {
    const rowCount = await this.getRowCount();
    for (let i = 0; i < rowCount; i++) {
      const row = this.tableRows.nth(i);
      const firstCell = row.locator('td').first();
      const strongText = await firstCell.locator('strong').textContent();
      
      if (strongText?.match(headerText)) {
        console.log(`Found ${headerText} row at index ${i}`);
        return row.locator('td').nth(1);
      }
    }
    throw new Error(`Failed to find row with header text: ${headerText}`);
  }

  async getStartDateElement() {
    return await this.findRowByHeaderText('start');
  }

  async getEndDateElement() {
    return await this.findRowByHeaderText('end');
  }

  async getAttendeesElement() {
    return await this.findRowByHeaderText('attendees');
  }

  async getFunctionSpaceElement() {
    // Function space is in the second row with empty <strong></strong> tag
    const rowCount = await this.getRowCount();
    let emptyStrongCount = 0;
    
    for (let i = 0; i < rowCount; i++) {
      const row = this.tableRows.nth(i);
      const firstCell = row.locator('td').first();
      const strongElement = firstCell.locator('strong');
      const strongText = await strongElement.textContent();
      
      // Look for rows with empty strong tag
      if (strongText === '') {
        emptyStrongCount++;
        if (emptyStrongCount === 2) {
          console.log(`Found function space row at index ${i} (second empty strong tag)`);
          return row.locator('td').nth(1);
        }
      }
    }
    throw new Error('Failed to find function space row (second row with empty strong tag)');
  }

  async getDescriptionElement() {
    return await this.findRowByHeaderText('Description');
  }

  async verifyStartDate(expectedDate: string) {
    const startDateElement = await this.getStartDateElement();
    await expect(startDateElement).toBeVisible({ timeout: 5000 });
    const dateText = await startDateElement.textContent() || '';
    
    // Extract date components for verification
    const [year, month, day] = expectedDate.split('-');
    
    // Verify date components are present in the displayed text
    expect(dateText).toContain(year);
    expect(dateText).toContain(parseInt(month).toString()); // Remove leading zero if present
    expect(dateText).toContain(parseInt(day).toString()); // Remove leading zero if present
    
    return dateText.trim();
  }

  async verifyEndDate(expectedDate: string) {
    const endDateElement = await this.getEndDateElement();
    await expect(endDateElement).toBeVisible({ timeout: 5000 });
    const dateText = await endDateElement.textContent() || '';
    
    // Extract date components for verification
    const [year, month, day] = expectedDate.split('-');
    
    // Verify date components are present in the displayed text
    expect(dateText).toContain(year);
    expect(dateText).toContain(parseInt(month).toString()); // Remove leading zero if present
    expect(dateText).toContain(parseInt(day).toString()); // Remove leading zero if present
    
    return dateText.trim();
  }

  async verifyAttendees(expectedCount: string) {
    const attendeesElement = await this.getAttendeesElement();
    await expect(attendeesElement).toBeVisible({ timeout: 5000 });
    const attendeesText = await attendeesElement.textContent() || '';
    expect(attendeesText.trim()).toBe(expectedCount);
  }

  async verifyFunctionSpace(expectedItems: string[]) {
    const functionSpaceElement = await this.getFunctionSpaceElement();
    await expect(functionSpaceElement).toBeVisible({ timeout: 5000 });
    const functionSpaceText = await functionSpaceElement.textContent() || '';
    for (const item of expectedItems) {
      expect(functionSpaceText).toContain(item);
    }
  }

  async verifyDescription(expectedDescription: string) {
    const descriptionElement = await this.getDescriptionElement();
    await expect(descriptionElement).toBeVisible({ timeout: 5000 });
    const descriptionText = await descriptionElement.textContent() || '';
    expect(descriptionText).toContain(expectedDescription);
  }
  
  async verifyDates(startDate: string, endDate: string) {
    // Verify start date
    await this.verifyStartDate(startDate);
    
    // Verify end date
    await this.verifyEndDate(endDate);
  }
}
