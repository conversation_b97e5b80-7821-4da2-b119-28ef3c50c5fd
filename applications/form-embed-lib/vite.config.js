"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/// <reference types="vitest" />
var config_1 = require("vitest/config");
exports.default = (0, config_1.defineConfig)({
    build: {
        lib: {
            entry: 'src/main.ts',
            name: 'SivFormEmbed',
            fileName: function (format) { return "siv-form-embed.".concat(format, ".js"); },
            formats: ['iife', 'es'],
        },
        rollupOptions: {
            output: {
                globals: {
                    '@iframe-resizer/parent': 'iframeResizer'
                },
                extend: true,
            },
        },
        sourcemap: true,
        minify: 'terser',
    },
    test: {
        globals: true,
        environment: 'jsdom',
        setupFiles: ['src/test/setup.ts'],
        include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
        reporters: ['default', ['junit', {
                    outputFile: './test-results/junit.xml'
                }], ['html', {
                    outputFile: './test-results/html'
                }]],
        coverage: {
            provider: 'v8',
            reporter: ['text', 'json', 'html'],
            reportsDirectory: './coverage',
            exclude: [
                'node_modules/',
                'src/test/',
            ]
        }
    },
});
