import { <PERSON>o } from "hono";
import logger from "./logger";
import { HTTPException } from "hono/http-exception";

// Create a test route that throws different types of errors
export function createTestErrorRoutes() {
  const app = new Hono();

  // Test standard Error
  app.get("/test/standard-error", () => {
    throw new Error("This is a test standard error");
  });

  // Test HTTPException
  app.get("/test/http-exception", () => {
    throw new HTTPException(400, { message: "This is a test HTTP exception" });
  });

  // Test async error
  app.get("/test/async-error", async () => {
    await new Promise((resolve) => setTimeout(resolve, 10));
    throw new Error("This is a test async error");
  });

  // Test unhandled rejection
  app.get("/test/unhandled-rejection", () => {
    Promise.reject(new Error("This is a test unhandled rejection"));
    return new Response("This will trigger unhandled rejection");
  });

  // Test logging directly
  app.get("/test/direct-log", () => {
    logger.error("This is a direct error log", {
      error: {
        message: "Test error message",
        stack: new Error().stack,
        type: "TestError",
      },
    });
    return new Response("Logged error directly");
  });

  return app;
}