import type { <PERSON><PERSON>Hand<PERSON> } from "hono";
import ddTrace from "dd-trace";
import type { Context, Next } from "hono";
import logger from "@/logger";

const tracer = ddTrace.tracer;

export interface DatadogOptions {
  serviceName?: string;
  trackQueryParams?: boolean;
  trackHeaders?: boolean;
  allowedHeaders?: string[];
  trackRequestBody?: boolean;
  trackResponseBody?: boolean;
  maxBodySize?: number;
  tags?: Record<string, string>;
}

const defaultOptions: DatadogOptions = {
  serviceName: process.env.DD_SERVICE || "local",
  trackQueryParams: true,
  trackHeaders: false,
  allowedHeaders: ["user-agent", "content-type"],
  trackRequestBody: false,
  trackResponseBody: false,
  maxBodySize: 1024 * 50, // 50KB
  tags: {},
};

export function datadogMiddleware(options: DatadogOptions = defaultOptions) {
  const config = { ...defaultOptions, ...options };

  return async function datadogMiddlewareInner(c: Context, next: Next) {
    // Start a new span for this request
    const span = tracer.startSpan("web.request");

    // Set basic request attributes
    span.setTag("http.method", c.req.method);
    span.setTag("http.url", c.req.url);

    // Get the matched route pattern
    const routePattern =
      c.req.matchedRoutes[c.req.matchedRoutes.length - 1].path;
    const resourceName = `${c.req.method} ${routePattern}`;
    span.setTag("http.route", routePattern);
    span.setTag("resource", resourceName);
    span.setTag("service.name", config.serviceName);

    // Add any custom tags
    if (config.tags) {
      Object.entries(config.tags).forEach(([key, value]) => {
        span.setTag(key, value);
      });
    }

    // Track query parameters if enabled
    if (config.trackQueryParams) {
      const url = new URL(c.req.url);
      url.searchParams.forEach((value, key) => {
        span.setTag(`http.query.${key}`, value);
      });
    }

    // Track allowed headers if enabled
    if (config.trackHeaders) {
      config.allowedHeaders?.forEach((header) => {
        const value = c.req.header(header);
        if (value) {
          span.setTag(`http.header.${header}`, value);
        }
      });
    }

    // Track request body if enabled
    if (config.trackRequestBody) {
      try {
        const body = await c.req.raw.text();
        if (body.length <= config.maxBodySize!) {
          span.setTag("http.request_body", body);
        }
      } catch (error) {
        // Ignore body parsing errors
      }
    }

    // Execute the route handler within the span's scope
    try {
      await tracer.scope().activate(span, async () => {
        await next();
      });
    } catch (e) {
      // Set error information on span
      span.setTag("error", true);
      if (e instanceof Error) {
        span.setTag("error.type", e.constructor.name);
        span.setTag("error.msg", e.message);
        span.setTag("error.stack", e.stack);
        
        // Log the error through pino for JSON formatting
        logger.error({
          error: {
            message: e.message,
            stack: e.stack,
            type: e.constructor.name,
          },
          path: c.req.path,
          method: c.req.method,
          url: c.req.url,
        }, "Request error in datadog middleware");
      } else {
        span.setTag("error.type", typeof e);
        span.setTag("error.msg", JSON.stringify(e));
        span.setTag("http.status_code", 500);
        
        // Log non-Error objects
        logger.error({
          error: {
            value: e,
            type: typeof e,
          },
          path: c.req.path,
          method: c.req.method,
          url: c.req.url,
        }, "Non-Error thrown in datadog middleware");
      }

      throw e; // Re-throw to allow error handling middleware to process
    }

    // Handle any errors set by error handling middleware
    if (c.error) {
      span.setTag("error", true);
      if (c.error instanceof Error) {
        span.setTag("error.type", c.error.constructor.name);
        span.setTag("error.msg", c.error.message);
        span.setTag("error.stack", c.error.stack);
      } else {
        span.setTag("error.type", typeof c.error);
        span.setTag("error.msg", JSON.stringify(c.error));
      }

      // Set 500 response if none exists
      if (!c.res) {
        c.res = new Response("Internal Server Error", { status: 500 });
      }
    }

    // Track response if configured
    if (c.res) {
      const status = c.res.status;
      span.setTag("http.status_code", status);

      // Set error tags for HTTP error responses (if not already set by error)
      if (!c.error && status >= 500) {
        span.setTag("error", true);
        span.setTag("error.type", "Internal Server Error");
      } else if (!c.error && status >= 400) {
        span.setTag("error", true);
        span.setTag("error.type", "Client Error");
      }

      // Track response body if enabled
      if (config.trackResponseBody) {
        try {
          const clone = c.res.clone();
          const body = await clone.text();
          if (body.length <= config.maxBodySize!) {
            span.setTag("http.response_body", body);
          }
        } catch (error) {
          // Ignore response body parsing errors
        }
      }
    }

    // Always finish the span
    span.finish();
  };
}

// Usage example:
/*
import { Hono } from 'hono'
import { datadogMiddleware } from './datadog-middleware'

const app = new Hono()

// Basic usage
app.use('*', datadogMiddleware())

// With custom options
app.use('*', datadogMiddleware({
  serviceName: 'my-api',
  trackHeaders: true,
  allowedHeaders: ['user-agent', 'authorization'],
  trackRequestBody: true,
  trackResponseBody: true,
  tags: {
    'environment': 'production',
    'version': '1.0.0'
  }
}))

// Define your routes
app.get('/', (c) => c.text('Hello World!'))
*/
