import { describe, it, expect, vi, beforeEach } from "vitest";
import { Hono } from "hono";
import { HTTPException } from "hono/http-exception";
import { errorHandler } from "./error-handler";
import logger from "@/logger";

// Mock the logger
vi.mock("@/logger", () => ({
  default: {
    warn: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
  },
}));

describe("errorHandler", () => {
  let app: Hono;

  beforeEach(() => {
    vi.clearAllMocks();
    app = new Hono();
    app.onError(errorHandler);
  });

  it("should log client errors (4xx) as warnings", async () => {
    app.get("/test", () => {
      throw new HTTPException(400, { message: "Bad Request" });
    });

    const res = await app.request("/test");
    
    expect(res.status).toBe(400);
    const json = await res.json();
    expect(json).toEqual({
      error: {
        message: "Bad Request",
        type: "http_exception",
        status: 400,
      },
    });
    
    expect(logger.warn).toHaveBeenCalledWith(
      "Client error occurred",
      expect.objectContaining({
        message: "Bad Request",
        type: "HTTPException",
        path: "/test",
        method: "GET",
        status: 400,
      })
    );
    expect(logger.error).not.toHaveBeenCalled();
  });

  it("should log server errors (5xx) as errors", async () => {
    app.get("/test", () => {
      throw new HTTPException(500, { message: "Internal Server Error" });
    });

    const res = await app.request("/test");
    
    expect(res.status).toBe(500);
    const json = await res.json();
    expect(json).toEqual({
      error: {
        message: "Internal Server Error",
        type: "http_exception",
        status: 500,
      },
    });
    
    expect(logger.error).toHaveBeenCalledWith(
      "Server error occurred",
      expect.objectContaining({
        message: "Internal Server Error",
        type: "HTTPException",
        path: "/test",
        method: "GET",
        status: 500,
      })
    );
    expect(logger.warn).not.toHaveBeenCalled();
  });

  it("should log regular errors as server errors", async () => {
    app.get("/test", () => {
      throw new Error("Something went wrong");
    });

    const res = await app.request("/test");
    
    expect(res.status).toBe(500);
    const json = await res.json();
    expect(json).toEqual({
      error: {
        message: "Something went wrong",
        type: "internal_error",
        status: 500,
      },
    });
    
    expect(logger.error).toHaveBeenCalledWith(
      "Server error occurred",
      expect.objectContaining({
        message: "Something went wrong",
        type: "Error",
        path: "/test",
        method: "GET",
      })
    );
  });

  it("should handle async errors", async () => {
    app.get("/test", async () => {
      await new Promise((resolve) => setTimeout(resolve, 1));
      throw new Error("Async error");
    });

    const res = await app.request("/test");
    
    expect(res.status).toBe(500);
    expect(logger.error).toHaveBeenCalledWith(
      "Server error occurred",
      expect.objectContaining({
        message: "Async error",
        type: "Error",
      })
    );
  });

  it("should always include stack traces in logs", async () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = "production";
    
    app.get("/test", () => {
      throw new Error("Production error");
    });

    await app.request("/test");
    
    const logCall = vi.mocked(logger.error).mock.calls[0];
    const loggedError = logCall[1] as any;
    expect(loggedError.stack).toBeDefined();
    expect(loggedError.stack).toContain("Production error");
    
    process.env.NODE_ENV = originalEnv;
  });

  it("should hide error details from response in production", async () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = "production";
    
    app.get("/test", () => {
      throw new Error("Sensitive error message");
    });

    const res = await app.request("/test");
    const json = await res.json();
    
    // Response should not expose the actual error message in production
    expect(json).toEqual({
      error: {
        message: "Internal server error",
        type: "internal_error",
        status: 500,
      },
    });
    
    // But logs should contain the actual error
    const logCall = vi.mocked(logger.error).mock.calls[0];
    const loggedError = logCall[1] as any;
    expect(loggedError.message).toBe("Sensitive error message");
    
    process.env.NODE_ENV = originalEnv;
  });
});