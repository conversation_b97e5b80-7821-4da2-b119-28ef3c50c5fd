import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "hono";
import { HTTPException } from "hono/http-exception";
import logger from "@/logger";

/**
 * Global error handler middleware that ensures all errors are logged in JSON format
 * This middleware should be added early in the middleware stack to catch all errors
 */
export const errorHandler: ErrorHandler = (err, c) => {
  // Extract useful error information
  const errorInfo = {
    message: err.message,
    type: err.constructor.name,
    path: c.req.path,
    method: c.req.method,
    // Always include stack trace in logs for debugging
    stack: err.stack,
    // Include additional context if available
    ...(err instanceof HTTPException && err.status ? { status: err.status } : {}),
  };

  // Log the error with appropriate level
  if (err instanceof HTTPException && err.status < 500) {
    // Client errors (4xx) - log as warn
    logger.warn("Client error occurred", errorInfo);
  } else {
    // Server errors (5xx) or unknown errors - log as error
    logger.error("Server error occurred", errorInfo);
  }

  // Return appropriate response
  if (err instanceof HTTPException) {
    // For HTTPException, use the status and message from the exception
    return c.json(
      {
        error: {
          message: err.message,
          type: "http_exception",
          status: err.status,
        },
      },
      err.status,
    );
  }

  // For all other errors, return 500
  return c.json(
    {
      error: {
        message: process.env.NODE_ENV === "production" 
          ? "Internal server error" 
          : err.message,
        type: "internal_error",
        status: 500,
      },
    },
    500,
  );
};

/**
 * Async error wrapper for route handlers
 * Ensures that async errors are properly caught and passed to error handling middleware
 */
export function asyncHandler<T extends (...args: any[]) => Promise<any>>(
  handler: T
): T {
  return (async (...args: any[]) => {
    try {
      return await handler(...args);
    } catch (error) {
      // Re-throw to let the error handler middleware catch it
      throw error;
    }
  }) as T;
}