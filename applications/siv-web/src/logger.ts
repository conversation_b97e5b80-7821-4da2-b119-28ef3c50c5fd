import pino from "pino";

const COLOR = {
  GREEN: `\x1b[32m`,
  RED: `\x1b[31m`,
  WHITE: `\x1b[37m`,
  YELLOW: `\x1b[33m`,
  CYAN: `\x1b[36m`,
};

const LEVEL_COLORS = {
  FATAL: COLOR.RED,
  ERROR: COLOR.RED,
  WARN: COLOR.YELLOW,
  INFO: COLOR.GREEN,
  DEBUG: COLOR.GREEN,
  TRACE: COLOR.GREEN,
};

const isBrowser = typeof window !== "undefined";

// Initialize tracing objects outside the formatter
let ddTrace: any = null;
let logFormats: any = null;

// Only import dd-trace in Node.js environment and only once
if (!isBrowser) {
  // Use dynamic import to avoid browser issues
  import("dd-trace")
    .then((module) => {
      ddTrace = module.default;
    })
    .catch((err) => {
      console.error("Failed to load dd-trace:", err);
    });

  import("dd-trace/ext/formats")
    .then((module) => {
      // @ts-ignore
      logFormats = module.formats?.LOG || module.default?.LOG;
    })
    .catch((err) => {
      console.error("Failed to load dd-trace formats:", err);
    });
}

// Get Datadog tags from environment variables
const environment = process.env.DD_ENV || process.env.NODE_ENV || "development";
const service = process.env.DD_SERVICE || "siv-web";
const version = process.env.DD_VERSION || "unknown";

// Node.js config
const nodeConfig = {
  formatters: {
    level: (label: string) => {
      return { level: label.toUpperCase() };
    },
    bindings: () => {
      return {};
    },
    log: (object: Record<string, unknown>) => {
      const { msg, ...rest } = object;

      // Create the log record with Datadog tags
      const record: Record<string, unknown> = {
        msg,
        "@dd.env": environment,
        "@dd.service": service,
        "@dd.version": version,
        env: environment,
        service: service,
        version: version,
        ...(Object.keys(rest).length ? rest : {}),
      };

      // Only inject trace context if we have the required modules and we're in Node.js
      if (!isBrowser) {
        try {
          // Get the active span and inject trace context if available
          const span = ddTrace.scope().active();
          if (span) {
            ddTrace.inject(span.context(), logFormats, record);
          }
        } catch (error) {
          // Silently handle errors with tracing
          console.error("Error injecting trace context:", error);
        }
      }

      return record;
    },
  },
  timestamp: () => `,"time":"${new Date(Date.now()).toISOString()}"`,
  messageKey: "msg",
};

const browserConfig = {
  browser: {
    write: (logObj: any) => {
      const { level, msg, time, ...rest } = logObj;

      const levelUppercased = level.toUpperCase();
      const timeFormatted = new Date(time).toLocaleTimeString("en-US", {
        hour12: false,
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        fractionalSecondDigits: 3,
      });

      const LEVEL_COLOR =
        LEVEL_COLORS[levelUppercased as keyof typeof LEVEL_COLORS] ||
        COLOR.WHITE;

      // Format: [time] LEVEL message {context}
      console.log(
        `[${timeFormatted}] ${LEVEL_COLOR}${levelUppercased}${COLOR.WHITE} ${msg}`,
        Object.keys(rest).length ? rest : "",
      );
    },
    formatters: {
      level: (label: string) => {
        return { level: label };
      },
    },
  },
};

const logger = isBrowser
  ? pino(browserConfig)
  : pino({
      level: "info",
      ...(process.env.LOGGER_JSON === "true"
        ? nodeConfig
        : {
            transport: {
              target: "pino-pretty",
            },
          }),
    });

export default logger;
