import { MiddlewareHandler } from "hono";
import { getAuth, clerkMiddleware } from "@hono/clerk-auth";
import { User } from "@/user";
import logger from "./logger";
import { env } from "@/config/env";

// Define Hono variable types (reuse from auth.tsx)
export type Variables = {
  user: User | null;
  clerkAuth: ReturnType<typeof getAuth> | null;
};

// Export the clerk configuration for user-facing routes
export const getUserFacingClerkConfig = () => {
  const clerkSecretKey = env.USER_FACING_SIV_CLERK_SECRET_KEY;
  const publishableKey = env.USER_FACING_SIV_CLERK_PUBLISHABLE_KEY;

  return {
    secretKey: clerkSecretKey,
    publishableKey: publishableKey,
  };
};

// Export the clerk middleware configured for user-facing routes
export const userFacingClerkMiddleware = () => {
  const config = getUserFacingClerkConfig();
  return clerkMiddleware(config);
};

export const createUserFacingAuthMiddleware = (): MiddlewareHandler<{
  Variables: Variables;
}> => {
  const clerkSecretKey = env.USER_FACING_SIV_CLERK_SECRET_KEY;
  const disableAuthenticationForTesting = env.DISABLE_AUTH_FOR_TESTING;
  const publishableKey = env.USER_FACING_SIV_CLERK_PUBLISHABLE_KEY;

  logger.info(
    {
      hasClerkSecretKey: !!clerkSecretKey,
      clerkSecretKeyPrefix: clerkSecretKey?.substring(0, 7),
      disableAuthenticationForTesting,
      hasPublishableKey: !!publishableKey,
      publishableKeyPrefix: publishableKey?.substring(0, 7),
    },
    "User-facing auth middleware configuration",
  );

  if (!disableAuthenticationForTesting) {
    if (!clerkSecretKey) {
      throw new Error(
        "USER_FACING_SIV_CLERK_SECRET_KEY environment variable is required",
      );
    }

    if (!publishableKey) {
      throw new Error(
        "USER_FACING_SIV_CLERK_PUBLISHABLE_KEY environment variable is required",
      );
    }
  }

  const protect: MiddlewareHandler<{ Variables: Variables }> = async (
    c,
    next,
  ) => {
    const currentPath = new URL(c.req.url).pathname;

    let user: User | null = null;
    let clerkAuth = null;

    if (!disableAuthenticationForTesting) {
      // Get Clerk authentication info
      clerkAuth = getAuth(c);

      logger.debug(
        {
          path: currentPath,
          clerkAuth: {
            userId: clerkAuth?.userId,
            sessionId: clerkAuth?.sessionId,
            claims: clerkAuth?.sessionClaims,
          },
        },
        "[user-facing-protect] Clerk auth state",
      );

      if (clerkAuth?.userId) {
        // Transform Clerk user data into our generic User type
        user = {
          id: clerkAuth.userId,
          provider: "clerk", // Using Clerk authentication
        };
        logger.info(
          { userId: user.id, path: currentPath },
          "[user-facing-protect] User authenticated",
        );
      } else {
        logger.info(
          { path: currentPath },
          "[user-facing-protect] No Clerk userId found",
        );
      }
    } else {
      // Use test user for development/testing
      if (env.TEST_AUTH_EMAIL && env.TEST_AUTH_PASSWORD) {
        user = {
          id: "test-user-id",
          provider: "clerk", // Use "clerk" as provider for test users too
        };
        logger.info(
          { userId: user.id, path: currentPath },
          "[user-facing-protect] Using test user",
        );
      }
    }

    // Set user in context
    c.set("user", user);
    c.set("clerkAuth", clerkAuth);

    // Check if user is authenticated
    if (!user) {
      logger.warn(
        { path: currentPath },
        "[user-facing-protect] User not authenticated, redirecting to sign-in",
      );

      // For user-facing routes, redirect to the user-facing sign-in page
      return c.redirect(
        `/app/login?redirect_url=${encodeURIComponent(currentPath)}`,
      );
    }

    await next();
  };

  return protect;
};
