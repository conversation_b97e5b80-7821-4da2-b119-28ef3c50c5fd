"use client";

import { useState, useEffect, useRef, useC<PERSON>back, useMemo } from "react";
import Joyride, {
  type CallBackProps,
  STATUS,
  EVENTS,
  ACTIONS,
} from "react-joyride-react-19";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle2, Circle, X, ChevronUp, ChevronDown } from "lucide-react";
import { ChecklistItemId } from "@/modules/deployment-rules-onboarding/types/lead-assignment";
import { toast } from "sonner";

interface OnboardingTourProps {
  run: boolean;
  setRun: (run: boolean) => void;
  onComplete?: () => void;
  onSkip?: () => void;
  tableOnly?: boolean;
  completedActions: string[];
  showChecklist?: boolean;
  setShowChecklist?: (show: boolean) => void;
  hasTeams: boolean;
  hasIndividuals: boolean;
  hasCriteria: boolean;
}

// Define the ChecklistItem type
export interface ChecklistItem {
  id: string;
  title: string;
  description: string;
  completed: boolean;
}

export default function OnboardingTour({
  run,
  setRun,
  onComplete,
  onSkip,
  tableOnly = false,
  completedActions = [],
  showChecklist = false,
  setShowChecklist = () => {},
  hasTeams = false,
  hasIndividuals = false,
  hasCriteria = false,
}: OnboardingTourProps) {
  // Use client-side only rendering to prevent hydration issues
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);
  const [stepIndex, setStepIndex] = useState(0);
  const [expanded, setExpanded] = useState(true);
  const [highlightedElement, setHighlightedElement] = useState<string | null>(
    null,
  );
  const highlightTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Reset step index when tour starts
  useEffect(() => {
    if (run) {
      setStepIndex(0);
      setExpanded(true);
    }
  }, [run]);

  // Cleanup highlight timeout on unmount
  useEffect(() => {
    return () => {
      if (highlightTimeoutRef.current) {
        clearTimeout(highlightTimeoutRef.current);
      }
    };
  }, []);

  // Define tour steps for the table view only, with a stable 'id' property
  const rawTableSteps = useCallback(
    () => [
      {
        id: ChecklistItemId.DefineCriteria,
        target: '[data-testid="criteria-definition-button"]',
        content:
          "Define Your Criteria - Start by telling us what factors you look at when assigning leads, such as Geography, Room Count, or Event Type.",
        disableBeacon: true,
        placement: "bottom" as const,
      },
      {
        id: ChecklistItemId.ManageDefinitions,
        target: '[data-testid="manage-definitions-button"]',
        content:
          "Manage Definitions - If you've selected Geography or Room Count criteria, define your regions and ranges here to customize how leads are categorized.",
        placement: "bottom" as const,
      },
      {
        id: ChecklistItemId.AddTeams,
        target: '[data-testid="add-team-button"]',
        content:
          "Add Teams (Optional) - If your hotel uses teams (like Sales/Catering), create them first. Each team can have one rule that acts as an initial filter for leads before they reach team members.",
        placement: "bottom" as const,
      },
      {
        id: ChecklistItemId.AddTeamMembers,
        target: '[data-testid="add-individual-button"]',
        content:
          "Add Individuals - Add the individuals who will be receiving leads. They can be standalone or assigned to a team.",
        placement: "bottom" as const,
      },
      {
        id: ChecklistItemId.TableOverview,
        target: ".assignment-table tbody",
        content:
          "Table Overview - The table shows your teams and individuals. Team members appear indented under their team. Each column represents a criteria type you've activated.",
        placement: "top" as const,
      },
      {
        id: ChecklistItemId.TeamCriteria,
        target: 'tr[data-row-type="team"] td[data-column]',
        content:
          "Team Criteria - Click on any cell in a team row to set criteria for the entire team. Teams can have only one rule that serves as a filter for all team members.",
        placement: "right" as const,
      },
      {
        id: ChecklistItemId.IndividualCriteria,
        target: 'tr[data-row-type="individual"] td[data-column]',
        content:
          "Individual Criteria - Click on any cell in an individual row to set specific criteria. You can create multiple rules per individual using the 'Add Rule' button - leads matching ANY of the rules will be assigned. For team members, leads must match BOTH the team's single rule AND at least one of the individual's rules.",
        placement: "right" as const,
      },
      {
        id: ChecklistItemId.MultipleRules,
        target: '[data-testid="add-rule-button"]',
        content:
          "Multiple Rules - Individuals can have multiple rules. After creating your first rule, use the 'Add Rule' button to create additional assignment paths. This is useful for handling different types of leads, seasonal variations, or coverage for multiple regions. Each rule works as an alternative (OR logic) - leads matching ANY rule will be assigned.",
        placement: "left" as const,
      },
      {
        id: ChecklistItemId.ValidateRules,
        target: '[data-testid="check-issues-button"]',
        content:
          "Validate Your Rules - Check for potential issues like overlapping assignments or coverage gaps across all your rules.",
        placement: "bottom" as const,
      },
      {
        id: ChecklistItemId.SaveActivate,
        target: '[data-testid="save-activate-button"]',
        content:
          "Save & Activate - When satisfied with your rules, save and activate them to begin automatic lead routing.",
        placement: "bottom" as const,
      },
    ],
    [],
  );

  // Define tour steps for the full application, with a stable 'id' property
  const rawFullTourSteps = useCallback(
    () => [
      ...rawTableSteps(),
      {
        id: ChecklistItemId.TourComplete,
        target: "body",
        content:
          "You're All Set! - You've completed the tour of the Lead Assignment Rule Builder.",
        placement: "center" as const,
      },
    ],
    [rawTableSteps],
  );

  // Use the appropriate steps based on the tableOnly prop
  const rawTourSteps = tableOnly ? rawTableSteps() : rawFullTourSteps();

  // Strip out 'id' before passing to Joyride
  const joyrideSteps = useMemo(
    () => rawTourSteps.map(({ id, ...joyrideProps }) => joyrideProps),
    [rawTourSteps],
  );

  // Extract title and description from step content
  const parseStepContent = (
    content: string,
  ): { title: string; description: string } => {
    const parts = content.split(" - ");
    if (parts.length >= 2) {
      return {
        title: parts[0],
        description: parts[1],
      };
    }
    return {
      title: content,
      description: "",
    };
  };

  // Create checklist items based on tour steps, using the stable 'id'
  const checklistItems: ChecklistItem[] = rawTourSteps.map((step) => {
    const { title, description } = parseStepContent(step.content as string);
    return {
      id: step.id,
      title,
      description,
      completed: completedActions.includes(step.id),
    };
  });

  const handleJoyrideCallback = useCallback(
    (data: CallBackProps) => {
      const { status, type, index, action } = data;

      // Handle close button click
      if (action === ACTIONS.CLOSE) {
        setRun(false);
        setShowChecklist(true);
        if (onSkip) {
          onSkip();
        }
        return;
      }

      // Tour is finished or skipped
      if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
        setRun(false);
        // Show the checklist when the tour completes
        setShowChecklist(true);

        if (status === STATUS.FINISHED && onComplete) {
          onComplete();
        }
        if (status === STATUS.SKIPPED && onSkip) {
          onSkip();
        }
      }

      // Handle step changes
      if (type === EVENTS.STEP_AFTER) {
        const nextIndex = index + (action === "next" ? 1 : -1);
        setStepIndex(nextIndex);
      }

      // Handle target not found
      if (type === EVENTS.TARGET_NOT_FOUND) {
        // Skip to the next step if target not found
        const nextIndex = index + 1;
        if (nextIndex < rawTourSteps.length) {
          setStepIndex(nextIndex);
        } else {
          setRun(false);
        }
      }
    },
    [onComplete, onSkip, setRun, setShowChecklist, rawTourSteps.length],
  );

  // Function to highlight an element temporarily
  const highlightElement = (selector: string) => {
    // Clear any existing highlight
    if (highlightTimeoutRef.current) {
      clearTimeout(highlightTimeoutRef.current);
    }

    // Remove any existing highlight class
    if (highlightedElement) {
      const prevElement = document.querySelector(highlightedElement);
      if (prevElement) {
        prevElement.classList.remove("highlight-pulse");
      }
    }

    // Add highlight to the new element
    const element = document.querySelector(selector);
    if (element) {
      element.classList.add("highlight-pulse");
      setHighlightedElement(selector);

      // Scroll element into view if needed
      element.scrollIntoView({ behavior: "smooth", block: "center" });

      // Remove highlight after 3 seconds
      highlightTimeoutRef.current = setTimeout(() => {
        element.classList.remove("highlight-pulse");
        setHighlightedElement(null);
      }, 3000);
    }
  };

  const handleChecklistItemClick = (id: string) => {
    // Check prerequisites for certain checklist items
    if (id === ChecklistItemId.TeamCriteria) {
      // Check for teams first - that's the more immediate prerequisite
      if (!hasTeams) {
        toast.error("Please add a team first", {
          description:
            "You need to create at least one team before setting team criteria.",
        });
        return;
      }
      // Then check for criteria
      if (!hasCriteria) {
        toast.error("Please define your criteria first", {
          description:
            "You need to set up assignment criteria before configuring team rules.",
        });
        return;
      }
    }

    if (id === ChecklistItemId.IndividualCriteria) {
      // Check for individuals first
      if (!hasIndividuals) {
        toast.error("Please add an individual first", {
          description:
            "You need to add at least one individual before setting individual criteria.",
        });
        return;
      }
      // Then check for criteria
      if (!hasCriteria) {
        toast.error("Please define your criteria first", {
          description:
            "You need to set up assignment criteria before configuring individual rules.",
        });
        return;
      }
    }

    // Find the step with this ID (stable id)
    const step = rawTourSteps.find((step) => step.id === id);
    if (step && step.target) {
      // Highlight the element instead of starting the tour
      highlightElement(step.target);
    }
  };

  // Create the tour configuration options with useMemo to avoid recreating objects
  const joyrideStyles = useMemo(
    () => ({
      options: {
        primaryColor: "hsl(var(--primary))",
        zIndex: 10000,
        arrowColor: "hsl(var(--background))", // Ensure arrow matches tooltip background
      },
      spotlight: {
        backgroundColor: "rgba(255, 255, 255, 0.1)",
      },
      buttonNext: {
        backgroundColor: "hsl(var(--primary))",
      },
      buttonBack: {
        marginRight: 10,
      },
    }),
    [],
  );

  const floaterProps = useMemo(
    () => ({
      disableAnimation: true, // Helps prevent multiple React root creations
    }),
    [],
  );

  return (
    <>
      {/* Add a style tag for the highlight pulse animation */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
        @keyframes highlight-pulse {
          0% { 
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5),
                        0 0 0 0 rgba(59, 130, 246, 0.3) inset;
          }
          70% { 
            box-shadow: 0 0 0 10px rgba(59, 130, 246, 0),
                        0 0 0 5px rgba(59, 130, 246, 0) inset;
          }
          100% { 
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0),
                        0 0 0 0 rgba(59, 130, 246, 0) inset;
          }
        }
        
        .highlight-pulse {
          animation: highlight-pulse 2s infinite;
          position: relative;
          z-index: 40;
          outline: 2px solid rgba(59, 130, 246, 0.5);
          outline-offset: 2px;
        }
        
        /* Ensure table cells maintain proper styling when highlighted */
        td.highlight-pulse, 
        th.highlight-pulse {
          background-color: rgba(59, 130, 246, 0.1) !important;
        }
        
        /* Special handling for table tbody highlight */
        tbody.highlight-pulse {
          outline: 3px solid rgba(59, 130, 246, 0.5);
          outline-offset: -1px;
        }
      `,
        }}
      />

      {/* Only render Joyride on the client to prevent hydration mismatch */}
      {isMounted && (
        <Joyride
          steps={joyrideSteps}
          run={run}
          stepIndex={stepIndex}
          callback={handleJoyrideCallback}
          continuous={true}
          showProgress={true}
          showSkipButton={true}
          disableScrolling={false}
          disableOverlayClose={false}
          spotlightClicks={false}
          floaterProps={floaterProps}
          styles={joyrideStyles}
        />
      )}

      {/* Only render checklist on the client to prevent hydration mismatch */}
      {isMounted && showChecklist && (
        <OnboardingChecklist
          items={checklistItems}
          onItemClick={handleChecklistItemClick}
          onClose={() => setShowChecklist(false)}
          title="Lead Assignment Setup"
          expanded={expanded}
          setExpanded={setExpanded}
        />
      )}
    </>
  );
}

interface OnboardingChecklistProps {
  items: ChecklistItem[];
  onItemClick: (id: string) => void;
  onClose: () => void;
  title: string;
  expanded: boolean;
  setExpanded: (expanded: boolean) => void;
}

function OnboardingChecklist({
  items,
  onItemClick,
  onClose,
  title,
  expanded,
  setExpanded,
}: OnboardingChecklistProps) {
  const completedCount = items.filter((item) => item.completed).length;

  return (
    <Card className="w-80 shadow-lg fixed bottom-4 right-4 z-50 max-h-[80vh] flex flex-col">
      <CardHeader className="pb-2 pt-4 px-4 flex flex-row items-center justify-between space-y-0">
        <CardTitle className="text-md font-medium">{title}</CardTitle>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7"
            onClick={() => setExpanded(!expanded)}
            aria-label={expanded ? "Collapse checklist" : "Expand checklist"}
          >
            {expanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronUp className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7"
            onClick={onClose}
            aria-label="Close checklist"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="px-4 pb-4 overflow-auto">
        <div className="text-xs text-muted-foreground mb-2">
          {completedCount} of {items.length} steps completed
        </div>
        {expanded && (
          <ul className="space-y-2">
            {items.map((item) => (
              <li
                key={item.id}
                className={`flex items-start gap-2 p-2 rounded-md cursor-pointer hover:bg-muted transition-colors ${
                  item.completed ? "text-muted-foreground" : ""
                }`}
                onClick={() => onItemClick(item.id)}
              >
                {item.completed ? (
                  <CheckCircle2
                    className="h-5 w-5 text-primary shrink-0 mt-0.5"
                    data-testid="checklist-complete-icon"
                  />
                ) : (
                  <Circle
                    className="h-5 w-5 text-muted-foreground shrink-0 mt-0.5"
                    data-testid="checklist-incomplete-icon"
                  />
                )}
                <div>
                  <div className="font-medium text-sm">{item.title}</div>
                  <div className="text-xs text-muted-foreground">
                    {item.description}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </CardContent>
    </Card>
  );
}
