export type AssignmentStrategy = "team" | "individual";

export type CriteriaTypeString =
  | "geography"
  | "roomCount"
  | "eventType"
  | "industry"
  | "eventNeeds"
  | "dayOfMonth";

export type CriterionValue =
  | { type: "Any" }
  | { type: "Specific"; values: string[] };

export const CRITERION_ANY: CriterionValue = { type: "Any" };

// NEW: A self-contained rule
export interface AssignmentRule {
  id: string; // Unique ID for the rule
  criteria: Partial<Record<CriteriaTypeString, CriterionValue | null>>;
}

export interface Team {
  id: string;
  name: string;
  /** @deprecated Use 'rules' instead */
  criteria?: Partial<Record<CriteriaTypeString, CriterionValue | null>>;
  /** @deprecated Use 'rules' instead */
  exceptions?: Exception[];
  /**
   * Assignment rules for the team.
   * NOTE: While this is an array for consistency with individuals,
   * teams are limited to exactly ONE rule in the UI.
   * This single rule acts as a filter that all team members inherit.
   */
  rules: AssignmentRule[];
}

export interface Individual {
  id: string;
  name: string;
  firstName?: string;
  lastName?: string;
  title: string;
  email: string;
  phone: string;
  teamId?: string | null;
  /** @deprecated Use 'rules' instead */
  criteria?: Partial<Record<CriteriaTypeString, CriterionValue | null>>;
  /** @deprecated Use 'rules' instead */
  exceptions?: Exception[];
  rules: AssignmentRule[]; // NEW: Multiple rules per individual
  isNewlyCreated?: boolean;
}

export interface Exception {
  id: string;
  entityType?: "team" | "individual";
  entityId?: string;
  conditions: ExceptionCondition[];
  action: "skip" | "redirect";
  redirectToId?: string;
  redirectToType?: "team" | "individual";
  _toBeDeleted?: boolean; // Added flag to mark exceptions for deletion
}

export type ExceptionOperator = "equals" | "not_equals" | "in" | "not_in";

export interface ExceptionCondition {
  criteriaType: CriteriaTypeString;
  operator: ExceptionOperator;
  value: string;
}

export interface CriteriaState {
  geography: {
    active: boolean;
    regions: GeographyRegion[];
  };
  roomCount: {
    active: boolean;
    ranges: RoomCountRange[];
  };
  eventType: {
    active: boolean;
    options: string[];
  };
  industry: {
    active: boolean;
    options: string[];
  };
  eventNeeds: {
    active: boolean;
    options: string[];
  };
  dayOfMonth: {
    active: boolean;
    options: string[];
  };
}

export interface GeographyRegion {
  id: string;
  name: string;
  countries: string[];
  usStates?: string[];
  isAllOthers?: boolean;
  isAllNonUS?: boolean;
  isAllOtherUSStates?: boolean;
  isPredefined?: boolean;
  predefinedType?:
    | "USA_ALL_STATES"
    | "USA_ALL_OTHER_STATES"
    | "ALL_NON_US_COUNTRIES"
    | "ALL_OTHER_COUNTRIES";
}

export interface RoomCountRange {
  id: string;
  name: string;
  condition: "less" | "greater" | "range";
  minValue?: number;
  maxValue?: number;
}

export interface CoverageGap {
  id: string; // A unique identifier for the gap
  description: string; // Human-readable description of the gap
  missingCombination: Array<{
    criteriaType: string;
    value: string;
    displayValue?: string;
  }>;
}

export enum ChecklistItemId {
  DefineCriteria = "define-criteria",
  ManageDefinitions = "manage-definitions",
  AddTeams = "add-teams",
  AddTeamMembers = "add-team-members",
  TableOverview = "table-overview",
  TeamCriteria = "team-criteria",
  IndividualCriteria = "individual-criteria",
  MultipleRules = "multiple-rules",
  ValidateRules = "validate-rules",
  SaveActivate = "save-activate",
  TourComplete = "tour-complete",
  ViewAssignments = "view-assignments",
  ConfigureCriteria = "configure-criteria",
  // HandleExceptions = "handle-exceptions", // Removed in v2 - no longer support exceptions
}
