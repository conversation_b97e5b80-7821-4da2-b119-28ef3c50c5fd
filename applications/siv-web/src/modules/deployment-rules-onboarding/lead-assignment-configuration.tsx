"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  AlertTriangleIcon,
  CheckSquare,
  HelpCircle,
  InfoIcon,
  User,
  Users,
  ChevronRight,
  ChevronDown,
} from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import AssignmentTable from "@/modules/deployment-rules-onboarding/assignment-table";
import AssignmentSummary from "@/modules/deployment-rules-onboarding/assignment-summary";
import IndividualDetailsModal from "@/modules/deployment-rules-onboarding/individual-details-modal";
import ConfigureTeamModal from "@/modules/deployment-rules-onboarding/configure-team-modal";
import OnboardingTour from "@/modules/deployment-rules-onboarding/onboarding-tour";
import { GeographyDefinitionModal } from "@/modules/deployment-rules-onboarding/edit-geography-rule-criteria-modal";
import RoomCountDefinitionModal from "@/modules/deployment-rules-onboarding/room-count-definition-modal";
import ManageGeographyRegionsModal from "@/modules/deployment-rules-onboarding/manage-geography-regions-modal";
import {
  cleanupInvalidCriteriaValues,
  isCriteriaValueInUse,
} from "@/modules/deployment-rules-onboarding/utils/usage-checks";
import {
  validateAssignmentConfiguration,
  type ValidationConfig,
  type ValidationResult,
} from "@/modules/deployment-rules-onboarding/utils/validation-service";
import type { OverlapDetail } from "@/modules/deployment-rules-onboarding/utils/overlap-detection";
import type {
  AssignmentStrategy,
  CoverageGap,
  CriteriaTypeString,
  GeographyRegion,
  Individual,
  RoomCountRange,
  Team,
} from "./types/lead-assignment";
import { ChecklistItemId } from "./types/lead-assignment";
import { CoverageGapTable } from "./components/coverage-gap-table";
import { SlideInPanel } from "./components/slide-in-panel";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { CriteriaCheckbox } from "./components/CriteriaCheckbox";
import { SingleOverlapDetailDisplay, CriteriaDetail } from "./components/single-overlap-detail-display";

// Interface for rich overlap information used in the UI
interface RichOverlapInfo {
  entity1: Individual | Team;
  entity2: Individual | Team;
  details: {
    overlaps: boolean;
    criteriaDetails: Array<CriteriaDetail>;
  };
  summary: string;
}

// Helper to convert v1 entities to v2 format (with rules array)
function convertToV2Format(
  individuals: Individual[],
  teams: Team[],
): { individualsV2: Individual[]; teamsV2: Team[] } {
  const individualsV2 = individuals.map((ind) => {
    // If the individual already has rules (from v2 updates), use them
    if (ind.rules && ind.rules.length > 0) {
      return ind;
    }

    // Otherwise, convert v1 criteria to a single rule
    return {
      ...ind,
      rules:
        ind.criteria &&
        Object.keys(ind.criteria).some(
          (k) => ind.criteria?.[k as CriteriaTypeString] !== null,
        )
          ? [
              {
                id: crypto.randomUUID(),
                criteria: ind.criteria,
              },
            ]
          : [
              {
                id: crypto.randomUUID(),
                criteria: {
                  geography: null,
                  roomCount: null,
                  eventType: null,
                  industry: null,
                  eventNeeds: null,
                  dayOfMonth: null,
                },
              },
            ],
    };
  });

  const teamsV2 = teams.map((team) => {
    // If the team already has rules (from v2 updates), use them
    if (team.rules && team.rules.length > 0) {
      return team;
    }

    // Otherwise, convert v1 criteria to a single rule
    return {
      ...team,
      rules:
        team.criteria &&
        Object.keys(team.criteria).some(
          (k) => team.criteria?.[k as CriteriaTypeString] !== null,
        )
          ? [
              {
                id: crypto.randomUUID(),
                criteria: team.criteria,
              },
            ]
          : [
              {
                id: crypto.randomUUID(),
                criteria: {
                  geography: null,
                  roomCount: null,
                  eventType: null,
                  industry: null,
                  eventNeeds: null,
                  dayOfMonth: null,
                },
              },
            ],
    };
  });

  return { individualsV2, teamsV2 };
}

export default function LeadAssignmentConfiguration() {
  // State to track if we're on the client side to prevent hydration mismatch
  const [isClient, setIsClient] = useState(false);

  // Run once after initial render to set client state
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Add CSS variable for primary color in RGB format for the highlight animation
  useEffect(() => {
    // Only run in the browser
    if (typeof window !== "undefined") {
      // Extract RGB values from the primary color
      // const primaryColor = getComputedStyle(document.documentElement).getPropertyValue("--primary").trim()

      // Set a fixed RGB value to avoid hydration issues
      document.documentElement.style.setProperty(
        "--primary-rgb",
        "14, 165, 233",
      );
    }
  }, []);

  // UI state
  const [activeCriteria, setActiveCriteria] = useState({
    geography: false,
    roomCount: false,
    eventType: false,
    industry: false,
    eventNeeds: false,
    dayOfMonth: false,
  });

  // Assignment strategy state
  const [assignmentStrategy, setAssignmentStrategy] =
    useState<AssignmentStrategy>("individual");

  // Teams state
  const [teams, setTeams] = useState<Team[]>([]);

  // Individuals state
  const [individuals, setIndividuals] = useState<Individual[]>([
    {
      id: "1",
      name: "John Smith",
      firstName: "John",
      lastName: "Smith",
      title: "Sales Manager",
      email: "<EMAIL>",
      phone: "************",
      teamId: null,
      criteria: {
        geography: null,
        roomCount: null,
        eventType: null,
        industry: null,
        eventNeeds: null,
        dayOfMonth: null,
      },
      rules: [
        {
          id: crypto.randomUUID(),
          criteria: {
            geography: null,
            roomCount: null,
            eventType: null,
            industry: null,
            eventNeeds: null,
            dayOfMonth: null,
          },
        },
      ], // v2 format uses rules array - start with one empty rule
    },
  ]);

  // Store draft individual data when creating a team in the middle of adding an individual
  const [draftIndividual, setDraftIndividual] =
    useState<Partial<Individual> | null>(null);

  // Helper function to materialize team rules for individuals
  const materializeTeamRulesIfNeeded = (individual: Individual): Individual => {
    if (!individual.teamId) {
      return individual;
    }

    const team = teams.find((t) => t.id === individual.teamId);
    if (!team || !team.rules || team.rules.length === 0) {
      return individual;
    }

    // Check if individual has no rules or has empty/null rules
    const hasNoRules = !individual.rules || individual.rules.length === 0;
    const hasEmptyRules =
      individual.rules &&
      individual.rules.length > 0 &&
      individual.rules.every((rule) =>
        Object.values(rule.criteria).every(
          (criterion) => criterion === null || criterion === undefined,
        ),
      );

    if (hasNoRules || hasEmptyRules) {
      // Create individual rules based on team rules
      const materializedRules = team.rules.map((teamRule) => ({
        id: crypto.randomUUID(),
        criteria: { ...teamRule.criteria },
      }));

      return {
        ...individual,
        rules: materializedRules,
      };
    }

    return individual;
  };

  const [showValidationPanel, setShowValidationPanel] = useState(false);
  const [gapsAcknowledged, setGapsAcknowledged] = useState(false);
  const [isLoadingValidation, setIsLoadingValidation] = useState(false);
  const [lastValidationTimestamp, setLastValidationTimestamp] = useState<
    number | null
  >(null);
  const [showCriteriaModal, setShowCriteriaModal] = useState(false);
  const [showStrategyModal, setShowStrategyModal] = useState(false);
  const [showGeographyModal, setShowGeographyModal] = useState(false);
  const [showRoomCountModal, setShowRoomCountModal] = useState(false);
  const [showManageDefinitionsModal, setShowManageDefinitionsModal] =
    useState(false);
  const [showManageGeographyModal, setShowManageGeographyModal] =
    useState(false);
  const [showAssignmentSummary, setShowAssignmentSummary] = useState(false);
  const [showAddIndividualModal, setShowAddIndividualModal] = useState(false);
  const [showAddTeamModal, setShowAddTeamModal] = useState(false);

  // Tour state
  const [runTour, setRunTour] = useState(false);

  // Auto-materialize team rules for existing individuals on mount
  useEffect(() => {
    let needsUpdate = false;
    const updatedIndividuals = individuals.map((individual) => {
      if (individual.teamId) {
        const team = teams.find((t) => t.id === individual.teamId);
        if (team && team.rules && team.rules.length > 0) {
          // Check if individual needs materialization
          const hasNoRules = !individual.rules || individual.rules.length === 0;
          const hasEmptyRules =
            individual.rules &&
            individual.rules.length > 0 &&
            individual.rules.every((rule) =>
              Object.values(rule.criteria).every(
                (criterion) => criterion === null || criterion === undefined,
              ),
            );

          if (hasNoRules || hasEmptyRules) {
            needsUpdate = true;
            return materializeTeamRulesIfNeeded(individual);
          }
        }
      }
      return individual;
    });

    if (needsUpdate) {
      setIndividuals(updatedIndividuals);
    }
  }, [teams]); // Run when teams change
  const [showChecklist, setShowChecklist] = useState(false);
  const [completedActions, setCompletedActions] = useState<string[]>([]);

  // State for geography regions and room count ranges
  const [geographyRegions, setGeographyRegions] = useState<GeographyRegion[]>(
    [],
  );

  // Initialize with a single dynamic "rest of world" system region
  useEffect(() => {
    if (geographyRegions.length === 0) {
      // Import Country from country-state-city to get all countries
      import("country-state-city").then(({ Country }) => {
        const allCountries = Country.getAllCountries();
        const allCountryCodes = allCountries.map((country) => country.isoCode);

        // Only include the single "All Countries/Regions" dynamic system region
        // This will automatically adjust as users define their own regions
        const defaultRegions: GeographyRegion[] = [
          {
            id: crypto.randomUUID(),
            name: "All Countries/Regions",
            countries: allCountryCodes,
            isPredefined: true,
            predefinedType: "ALL_OTHER_COUNTRIES",
            isAllOthers: true,
          },
        ];

        setGeographyRegions(defaultRegions);
      });
    }
  }, []);

  // Run initial validation when component mounts and data is ready
  useEffect(() => {
    // Only run validation if we have the necessary data and at least one criteria is active
    const hasActiveCriteria = Object.values(activeCriteria).some(
      (v) => v === true,
    );
    if (isClient && geographyRegions.length > 0 && hasActiveCriteria) {
      // Small delay to ensure all initial state is set
      const timeoutId = setTimeout(() => {
        checkForValidationIssues(individuals, teams).catch(console.error);
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [isClient, geographyRegions.length, individuals, teams, activeCriteria]); // Run when data changes

  // The system-managed regions are handled in manage-geography-regions-modal.ts
  const [roomCountRanges, setRoomCountRanges] = useState<RoomCountRange[]>([]);

  // State for validation issues
  const [overlappingRules, setOverlappingRules] = useState<string[]>([]);
  const [richOverlappingRules, setRichOverlappingRules] = useState<
    RichOverlapInfo[]
  >([]);
  const [coverageGaps, setCoverageGaps] = useState<CoverageGap[]>([]);
  const [expandedOverlapIndices, setExpandedOverlapIndices] = useState<
    Set<number>
  >(new Set());

  // New state to track which individuals and teams have overlapping rules
  const [overlappingIndividuals, setOverlappingIndividuals] = useState<
    string[]
  >([]);
  const [overlappingTeams, setOverlappingTeams] = useState<string[]>([]);

  // State to track individuals with blank criteria
  const [individualsWithBlankCriteria, setIndividualsWithBlankCriteria] =
    useState<string[]>([]);

  // V2 validation state
  const [validationResultV2, setValidationResultV2] =
    useState<ValidationResult | null>(null);

  // Track user actions for checklist
  useEffect(() => {
    // Check if criteria have been defined
    if (Object.values(activeCriteria).some((value) => value === true)) {
      markActionComplete(ChecklistItemId.DefineCriteria);
    }

    // Check if geography or room count definitions have been managed
    if (
      (activeCriteria.geography && geographyRegions.length > 1) ||
      (activeCriteria.roomCount && roomCountRanges.length > 0)
    ) {
      markActionComplete(ChecklistItemId.ManageDefinitions);
    }

    // Check if individuals have been added (beyond the default one)
    if (individuals.length > 1) {
      markActionComplete(ChecklistItemId.AddTeamMembers);
      markActionComplete(ChecklistItemId.ViewAssignments);
    }

    // Check if teams have been added
    if (teams.length > 0) {
      markActionComplete(ChecklistItemId.AddTeams);
    }

    // Check if any individual has configured criteria
    const hasCriteria = individuals.some(
      (individual) =>
        individual &&
        individual.criteria &&
        Object.values(individual.criteria).some(
          (criteriaArray) =>
            Array.isArray(criteriaArray) && criteriaArray.length > 0,
        ),
    );
    if (hasCriteria) {
      markActionComplete(ChecklistItemId.ConfigureCriteria);
    }

    // V2 doesn't have exceptions, so we skip this check

    // Check if validation has been run
    if (
      showValidationPanel ||
      overlappingRules.length > 0 ||
      coverageGaps.length > 0
    ) {
      markActionComplete(ChecklistItemId.ValidateRules);
    }

    // Check if gaps have been acknowledged (ready to save)
    if (gapsAcknowledged) {
      markActionComplete(ChecklistItemId.SaveActivate);
    }
  }, [
    activeCriteria,
    individuals,
    teams,
    showValidationPanel,
    overlappingRules,
    coverageGaps,
    gapsAcknowledged,
    geographyRegions.length,
    roomCountRanges.length,
  ]);

  const markActionComplete = (actionId: ChecklistItemId) => {
    setCompletedActions((prev) => {
      if (!prev.includes(actionId)) {
        return [...prev, actionId];
      }
      return prev;
    });
  };

  const handleAddIndividual = () => {
    // Reset any draft data when explicitly adding a new individual
    setDraftIndividual(null);
    setShowAddIndividualModal(true);
  };

  const handleAddTeam = () => {
    setShowAddTeamModal(true);
  };

  const handleSaveNewIndividual = (individual: Individual) => {
    // Ensure individual has rules array with at least one empty rule
    const individualWithDefaults = {
      ...individual,
      rules: individual.rules && individual.rules.length > 0 
        ? individual.rules 
        : [
            {
              id: crypto.randomUUID(),
              criteria: {
                geography: null,
                roomCount: null,
                eventType: null,
                industry: null,
                eventNeeds: null,
                dayOfMonth: null,
              },
            },
          ],
    };

    // Apply team rule materialization if needed
    const individualToStore = materializeTeamRulesIfNeeded(
      individualWithDefaults,
    );

    setIndividuals([...individuals, individualToStore]);
    setShowAddIndividualModal(false);

    // Clear the draft data after successfully saving the individual
    setDraftIndividual(null);

    // Update validation issues when an individual is added
    checkForValidationIssues([...individuals, individualToStore], teams).catch(
      console.error,
    );
  };

  const handleSaveNewTeam = (team: Team) => {
    // Ensure team has a properly initialized criteria object and rules array
    const updatedTeam = {
      ...team,
      criteria: {
        geography: team.criteria?.geography || null,
        roomCount: team.criteria?.roomCount || null,
        eventType: team.criteria?.eventType || null,
        industry: team.criteria?.industry || null,
        eventNeeds: team.criteria?.eventNeeds || null,
        dayOfMonth: team.criteria?.dayOfMonth || null,
      },
      rules: team.rules && team.rules.length > 0 
        ? team.rules 
        : [
            {
              id: crypto.randomUUID(),
              criteria: {
                geography: null,
                roomCount: null,
                eventType: null,
                industry: null,
                eventNeeds: null,
                dayOfMonth: null,
              },
            },
          ],
    };

    setTeams([...teams, updatedTeam]);
    setShowAddTeamModal(false);

    // Update validation issues when a team is added
    checkForValidationIssues(individuals, [...teams, updatedTeam]).catch(
      console.error,
    );

    // Reopen the individual modal with the saved draft data if available
    if (draftIndividual) {
      // Set the teamId to the newly created team
      const updatedDraft = {
        ...draftIndividual,
        teamId: team.id,
      };
      setDraftIndividual(updatedDraft);

      // Use setTimeout to reopen individual modal after team modal closes
      setTimeout(() => {
        setShowAddIndividualModal(true);
      }, 50);
    }
  };

  const removeIndividual = (id: string) => {
    const updatedIndividuals = individuals.filter(
      (individual) => individual.id !== id,
    );
    setIndividuals(updatedIndividuals);

    // Update validation issues when an individual is removed
    checkForValidationIssues(updatedIndividuals, teams).catch(console.error);

    // Clear overlapping individuals that were removed
    setOverlappingIndividuals((prev) =>
      prev.filter((individualId) =>
        updatedIndividuals.some((ind) => ind.id === individualId),
      ),
    );
  };

  const removeTeam = (id: string) => {
    const updatedTeams = teams.filter((team) => team.id !== id);

    // Update individuals that were part of this team
    const updatedIndividuals = individuals.map((individual) =>
      individual.teamId === id ? { ...individual, teamId: null } : individual,
    );

    setTeams(updatedTeams);
    setIndividuals(updatedIndividuals);

    // Update validation issues
    checkForValidationIssues(updatedIndividuals, updatedTeams).catch(
      console.error,
    );

    // Clear overlapping teams that were removed
    setOverlappingTeams((prev) =>
      prev.filter((teamId) => updatedTeams.some((team) => team.id === teamId)),
    );
  };

  const updateIndividual = (updatedIndividual: Individual) => {
    // Always use v2 format with rules
    const individualWithDefaults = {
      ...updatedIndividual,
      // Ensure rules array exists with at least one empty rule
      rules: updatedIndividual.rules && updatedIndividual.rules.length > 0 
        ? updatedIndividual.rules 
        : [
            {
              id: crypto.randomUUID(),
              criteria: {
                geography: null,
                roomCount: null,
                eventType: null,
                industry: null,
                eventNeeds: null,
                dayOfMonth: null,
              },
            },
          ],
      // Keep the old criteria for backward compatibility
      criteria: updatedIndividual.criteria || {
        geography: null,
        roomCount: null,
        eventType: null,
        industry: null,
        eventNeeds: null,
        dayOfMonth: null,
      },
    };

    // Apply team rule materialization if needed
    const individualToStore = materializeTeamRulesIfNeeded(
      individualWithDefaults,
    );

    const updatedIndividuals = individuals.map((individual) =>
      individual.id === individualToStore.id ? individualToStore : individual,
    );
    setIndividuals(updatedIndividuals);

    // Update validation issues when an individual is updated
    checkForValidationIssues(updatedIndividuals, teams).catch(console.error);
  };

  const updateTeam = (updatedTeam: Team) => {
    // Always use v2 format with rules
    const teamToStore = {
      ...updatedTeam,
      // Ensure rules array exists with at least one empty rule
      rules: updatedTeam.rules && updatedTeam.rules.length > 0 
        ? updatedTeam.rules 
        : [
            {
              id: crypto.randomUUID(),
              criteria: {
                geography: null,
                roomCount: null,
                eventType: null,
                industry: null,
                eventNeeds: null,
                dayOfMonth: null,
              },
            },
          ],
      // Keep the old criteria for backward compatibility
      criteria: updatedTeam.criteria || {
        geography: null,
        roomCount: null,
        eventType: null,
        industry: null,
        eventNeeds: null,
        dayOfMonth: null,
      },
    };

    const updatedTeams = teams.map((team) =>
      team.id === teamToStore.id ? teamToStore : team,
    );
    setTeams(updatedTeams);

    // Update validation issues when a team is updated
    checkForValidationIssues(individuals, updatedTeams).catch(console.error);
  };

  const handleCriteriaSave = (newCriteria: any) => {
    console.log("Saving criteria:", newCriteria);
    console.log("Previous activeCriteria:", activeCriteria);

    setActiveCriteria(newCriteria);
    setShowCriteriaModal(false);

    // If geography or room count is activated for the first time, show definition modal after criteria modal closes
    setTimeout(() => {
      console.log("After timeout - activeCriteria:", activeCriteria);
      console.log("newCriteria:", newCriteria);

      if (
        newCriteria.geography &&
        !activeCriteria.geography &&
        geographyRegions.length === 0
      ) {
        setShowManageGeographyModal(true);
      } else if (
        newCriteria.roomCount &&
        !activeCriteria.roomCount &&
        roomCountRanges.length === 0
      ) {
        setShowRoomCountModal(true);
      }
    }, 50);

    // Update validation issues when criteria change
    checkForValidationIssues(individuals, teams).catch(console.error);
  };

  const handleStrategyChange = (strategy: AssignmentStrategy) => {
    setAssignmentStrategy(strategy);
    setShowStrategyModal(false);

    // Update validation issues when strategy changes
    checkForValidationIssues(individuals, teams).catch(console.error);
  };

  const handleManageDefinitions = () => {
    setShowManageDefinitionsModal(true);
  };

  // Simple, direct modal transitions without trying to track pending states
  const handleManageDefinitionsSelection = (
    type: "geography" | "roomCount",
  ) => {
    // Close the current modal
    setShowManageDefinitionsModal(false);

    // Set a timeout to open the next modal after the current one has closed
    setTimeout(() => {
      if (type === "geography") {
        setShowManageGeographyModal(true);
      } else {
        setShowRoomCountModal(true);
      }
    }, 50);
  };

  // Helper function to check if a geography region is being used in any assignment rules
  const isGeographyRegionInUse = (regionId: string): boolean => {
    console.log(`Checking if region ${regionId} is in use...`);
    return isCriteriaValueInUse("geography", regionId, individuals, teams);
  };

  // Helper function to check if a room count range is being used in any assignment rules
  const isRoomCountRangeInUse = (rangeId: string): boolean => {
    console.log(`Checking if range ${rangeId} is in use...`);
    return isCriteriaValueInUse("roomCount", rangeId, individuals, teams);
  };

  const handleSaveGeographyRegions = (regions: GeographyRegion[]) => {
    console.log(
      "DEBUG: Received regions from manage geography modal:",
      regions,
    );

    // Ensure we update state with all regions, including system-managed ones
    setGeographyRegions(regions);
    setShowManageGeographyModal(false);

    // Update validation issues as needed
    checkForValidationIssues(individuals, teams).catch(console.error);

    // Log the regions state after update to confirm it's correct
  };

  // Function to clean up any invalid geography region values from assignment criteria
  const cleanupInvalidGeographyValues = (validRegionIds: string[]) => {
    // Use the utility function to clean up invalid values from individuals and teams
    const { individuals: updatedIndividuals, teams: updatedTeams } =
      cleanupInvalidCriteriaValues(
        "geography",
        validRegionIds,
        individuals,
        teams,
      );

    // Update state with cleaned up values
    setIndividuals(updatedIndividuals);
    setTeams(updatedTeams);

    console.log(
      "DEBUG: Cleaned up invalid geography values. Valid regions:",
      validRegionIds,
    );
  };

  // Function to clean up any invalid room count range values from assignment criteria
  const cleanupInvalidRoomCountValues = (validRangeIds: string[]) => {
    // Use the utility function to clean up invalid values from individuals and teams
    const { individuals: updatedIndividuals, teams: updatedTeams } =
      cleanupInvalidCriteriaValues(
        "roomCount",
        validRangeIds,
        individuals,
        teams,
      );

    // Update state with cleaned up values
    setIndividuals(updatedIndividuals);
    setTeams(updatedTeams);

    console.log(
      "DEBUG: Cleaned up invalid room count values. Valid ranges:",
      validRangeIds,
    );
  };

  // Helper functions moved to utils/validation-service.ts

  const [overlapSamplingWarning, setOverlapSamplingWarning] = useState(false);

  const checkForValidationIssues = async (
    currentIndividuals: Individual[],
    currentTeams: Team[],
  ) => {
    // Convert to v2 format and use v2 validation
    const { individualsV2, teamsV2 } = convertToV2Format(
      currentIndividuals,
      currentTeams,
    );

    const config: ValidationConfig = {
      individuals: individualsV2,
      teams: teamsV2,
      activeCriteria,
      geographyRegions,
      roomCountRanges,
      gapsAcknowledged,
    };

    console.log(
      "DEBUG: Teams being passed to validation:",
      teamsV2.map((team) => ({
        id: team.id,
        name: team.name,
        rules: team.rules,
      })),
    );
    console.log("DEBUG: Assignment strategy:", assignmentStrategy);

    const resultV2 = validateAssignmentConfiguration(config);
    console.log("Validation result:", {
      hasConflicts: resultV2.entityIdsWithConflicts.size > 0,
      conflictIds: Array.from(resultV2.entityIdsWithConflicts),
      redundancyIds: Array.from(resultV2.entityIdsWithRedundanciesOnly),
      allOverlaps: resultV2.allOverlaps,
      individualsV2: individualsV2.map((ind) => ({
        id: ind.id,
        name: ind.name,
        teamId: ind.teamId,
        rules: ind.rules,
      })),
      activeCriteria,
    });
    setValidationResultV2(resultV2);

    // Map v2 results to state
    const conflictOverlaps = resultV2.allOverlaps.filter(
      (o) => o.type === "conflict",
    );
    const overlappingRuleStrings = conflictOverlaps.map(
      (o) => `${o.entity1Name} and ${o.entity2Name} have overlapping rules`,
    );

    setOverlappingRules(overlappingRuleStrings);

    // Convert v2 overlaps to rich format for the overlap details panel
    const richOverlaps = conflictOverlaps.map((overlap) => {
      // Find the actual entities
      const allEntities = [...currentIndividuals, ...currentTeams];
      const entity1 = allEntities.find((e) =>
        overlap.involvedEntityIds.includes(e.id),
      )!;
      const entity2 = allEntities.find(
        (e) => overlap.involvedEntityIds.includes(e.id) && e.id !== entity1.id,
      )!;

      // Create a simplified details structure for v2
      const details = {
        overlaps: true,
        criteriaDetails: Object.keys(activeCriteria)
          .filter((key) => activeCriteria[key as CriteriaTypeString])
          .map((criteriaType) => ({
            type: criteriaType as any,
            rule1Values: [],
            rule2Values: [],
            isOverlapping: true,
          })),
      };

      return {
        entity1,
        entity2,
        details,
        summary: overlap.summary,
      };
    });

    setRichOverlappingRules(richOverlaps);
    setOverlappingIndividuals(
      Array.from(resultV2.entityIdsWithConflicts).filter((id) =>
        currentIndividuals.some((ind) => ind.id === id),
      ),
    );
    setOverlappingTeams(
      Array.from(resultV2.entityIdsWithConflicts).filter((id) =>
        currentTeams.some((team) => team.id === id),
      ),
    );
    setOverlapSamplingWarning(false); // V2 doesn't use sampling
    setCoverageGaps(resultV2.coverageGaps);

    // Map rules with unset criteria to individuals with blank criteria for UI
    const indIdsWithUnsetCriteria = new Set(
      resultV2.rulesWithUnsetCriteria
        .filter((r) => r.entityType === "individual")
        .map((r) => r.entityId),
    );
    setIndividualsWithBlankCriteria(Array.from(indIdsWithUnsetCriteria));

    console.log(
      `V2 Validation: Found ${resultV2.coverageGaps.length} coverage gaps, ${resultV2.rulesWithUnsetCriteria.length} rules with unset criteria`,
    );

    // Return result for compatibility
    return {
      isValid: resultV2.isValid,
      hasOverlaps: resultV2.hasConflicts,
      hasCoverageGaps: resultV2.hasCoverageGaps,
      hasBlankCriteria: resultV2.hasUnsetCriteria,
      overlappingRules: overlappingRuleStrings,
      richOverlappingRules: richOverlaps,
      overlappingIndividualIds: Array.from(
        resultV2.entityIdsWithConflicts,
      ).filter((id) => currentIndividuals.some((ind) => ind.id === id)),
      overlappingTeamIds: Array.from(resultV2.entityIdsWithConflicts).filter(
        (id) => currentTeams.some((team) => team.id === id),
      ),
      coverageGaps: resultV2.coverageGaps,
      individualsWithBlankCriteria: Array.from(indIdsWithUnsetCriteria),
      isSampling: false,
    };
  };

  const handleConfigureCriteria = () => {
    setShowCriteriaModal(true);
  };

  const handleConfigureStrategy = () => {
    setShowStrategyModal(true);
  };

  const toggleOverlapExpanded = (index: number) => {
    setExpandedOverlapIndices((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  const runValidation = async () => {
    setIsLoadingValidation(true);

    // Reset any existing issues
    setCoverageGaps([]);
    setOverlappingRules([]);
    setRichOverlappingRules([]);
    setOverlappingIndividuals([]);
    setOverlappingTeams([]);

    try {
      // Run validation checks using the validation service
      const validationResult = await checkForValidationIssues(
        individuals,
        teams,
      );

      // Update timestamp to track when validation was last run
      setLastValidationTimestamp(Date.now());

      console.log("Validation result:", validationResult);
      return validationResult;
    } finally {
      setIsLoadingValidation(false);
    }
  };

  const handleCheckIssues = async () => {
    console.log("Checking for validation issues...");

    // Run validation and get detailed results
    const validationResult = await runValidation();

    // Always show validation panel, even if no issues were found
    // This is the explicit "Check for Issues" flow where users want to see results
    setShowValidationPanel(true);

    // If there are no issues, the panel will show a success message
    if (validationResult.isValid) {
      console.log(
        "No validation issues found or all gaps have been acknowledged",
      );
    } else {
      if (validationResult.hasOverlaps) {
        console.log(
          `Found ${validationResult.overlappingRules.length} rule overlaps`,
        );
      }

      if (validationResult.hasCoverageGaps) {
        console.log(
          `Found ${validationResult.coverageGaps.length} coverage gaps`,
        );
      }
    }

    console.log("Validation panel is now visible with results");
    return validationResult;
  };

  const handleSaveActivate = async () => {
    console.log("Save & Activate Rules clicked!");

    // Always run validation first
    setIsLoadingValidation(true);
    const validationResult = await runValidation();

    // Check for issues (overlaps, blank criteria, or unacknowledged gaps)
    if (
      validationResult.hasOverlaps ||
      validationResult.hasBlankCriteria ||
      (validationResult.hasCoverageGaps && !gapsAcknowledged)
    ) {
      // Show validation panel with issues
      setShowValidationPanel(true);

      if (validationResult.hasOverlaps) {
        console.log(
          `Cannot save/activate: Found ${validationResult.overlappingRules.length} rule overlaps`,
        );
        // User needs to fix overlaps before proceeding
        return;
      }

      if (validationResult.hasCoverageGaps && !gapsAcknowledged) {
        console.log(
          `Cannot save/activate: Found ${validationResult.coverageGaps.length} unacknowledged coverage gaps`,
        );
        // User needs to acknowledge gaps in the panel before proceeding
        return;
      }
    }

    // If we get here, either:
    // 1. There are no issues (no overlaps, no gaps)
    // 2. There are only gaps and they've been acknowledged
    console.log("Validation passed, proceeding with save and activate");

    // If the validation panel is open (because we just validated), close it
    setShowValidationPanel(false);

    // Show the success alert dialog instead of browser alert
    setShowSuccessAlert(true);
  };

  const handleStartTour = () => {
    setRunTour(true);
  };

  const handleToggleChecklist = () => {
    setShowChecklist(!showChecklist);
  };

  /**
   * Helper function to check if a lead scenario passes an entity's criteria
   * Similar to the function in overlap-detection.ts but simplified for this use case
   *
   * IMPORTANT BEHAVIOR NOTE:
   * Empty arrays are treated as "Any" meaning they match ALL possible values.
   * This applies throughout the application for all criteria types:
   * - Empty geography array = All regions
   * - Empty roomCount array = All room count ranges
   * - Empty eventType array = All event types
   * - etc.
   *
   * This is critical for correct coverage gap detection and ensuring that rules
   * with "Any" values (empty arrays) correctly match all possible scenarios.
   */
  const evaluateLeadAgainstCriteria = (
    leadScenario: Record<string, string>,
    ruleCriteriaValues: Record<string, string[]>,
    activeCriteriaTypes: string[],
    allPossibleValuesPerCriterion: Partial<Record<string, Set<string>>>,
  ): boolean => {
    // Check each active criteria type
    for (const critType of activeCriteriaTypes) {
      const ruleValuesForCritArray = ruleCriteriaValues[critType] || [];
      const scenarioValueForCrit = leadScenario[critType];

      // If the rule accepts any value for this criterion (empty array), it matches
      if (ruleValuesForCritArray.length === 0) {
        continue; // "Any" always matches - this criteria type is satisfied
      }

      // Handle missing values in the scenario
      if (scenarioValueForCrit === undefined) {
        // If scenario is missing this criterion but rule requires specific values, it doesn't match
        return false;
      }
      // Otherwise check if the value is included in the rule's values
      else if (!ruleValuesForCritArray.includes(scenarioValueForCrit)) {
        // Specific scenario value not found in rule's specific values
        return false;
      }
    }

    // The lead matches all criteria
    return true;
  };

  // Add state for the alert dialog
  const [showValidateAlert, setShowValidateAlert] = useState(false);
  // Add state for the success alert dialog
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  // Add state for the overlap alert dialog
  const [showOverlapAlert, setShowOverlapAlert] = useState(false);

  return (
    <div className="space-y-6">
      {/* Onboarding Tour Component */}
      <OnboardingTour
        run={runTour}
        setRun={setRunTour}
        onComplete={() => {
          console.log("Tour completed");
          setShowChecklist(true);
        }}
        onSkip={() => {
          console.log("Tour skipped");
          setShowChecklist(true);
        }}
        tableOnly={true}
        completedActions={completedActions}
        showChecklist={showChecklist}
        setShowChecklist={setShowChecklist}
        hasTeams={teams.length > 0}
        hasIndividuals={individuals.length > 0}
        hasCriteria={Object.values(activeCriteria).some((active) => active)}
      />

      <h1 className="text-3xl font-bold">
        Configure Your Lead Assignment Rules
      </h1>

      {/* Header Action Bar */}
      <div className="flex flex-wrap gap-3 relative">
        <Button
          variant="outline"
          onClick={handleConfigureCriteria}
          data-testid="criteria-definition-button"
        >
          Define Assignment Criteria
        </Button>

        <Button
          variant="outline"
          onClick={handleManageDefinitions}
          data-testid="manage-definitions-button"
        >
          Manage Definitions
        </Button>

        <Button
          variant="outline"
          onClick={handleAddTeam}
          data-testid="add-team-button"
        >
          <Users className="h-4 w-4 mr-2" /> Add Team
        </Button>

        <Button
          variant="outline"
          onClick={handleAddIndividual}
          data-testid="add-individual-button"
        >
          <User className="h-4 w-4 mr-2" /> Add Individual
        </Button>

        {isClient && (
          <Dialog open={showCriteriaModal} onOpenChange={setShowCriteriaModal}>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Select Your Assignment Factors</DialogTitle>
              </DialogHeader>
              <div className="py-4">
                <p className="text-sm text-muted-foreground mb-4">
                  Check the boxes for all the criteria your hotel uses to route
                  leads to sellers. You'll define specifics for Geography and
                  Room Count later if needed.
                </p>
                <div className="space-y-4">
                  <CriteriaCheckbox
                    id="geography"
                    label="Geography"
                    description="Leads are assigned based on their geographic origin. Ex. local, west coast, midwest"
                    checked={activeCriteria.geography}
                    onCheckedChange={(checked) =>
                      setActiveCriteria({
                        ...activeCriteria,
                        geography: checked === true,
                      })
                    }
                    data-testid="geography-criteria-checkbox"
                  />
                  <CriteriaCheckbox
                    id="roomCount"
                    label="Room Count"
                    description="Leads are assigned based on number of rooms reserved on peak night, or number of total rooms for event. Ex. More than 50 rooms, between 10 and 20 rooms"
                    checked={activeCriteria.roomCount}
                    onCheckedChange={(checked) =>
                      setActiveCriteria({
                        ...activeCriteria,
                        roomCount: checked === true,
                      })
                    }
                    data-testid="room-count-criteria-checkbox"
                  />
                  <CriteriaCheckbox
                    id="eventType"
                    label="Event Type"
                    description="Leads are assigned based on the type of event. Ex. Wedding, Corporate Meetings"
                    checked={activeCriteria.eventType}
                    onCheckedChange={(checked) =>
                      setActiveCriteria({
                        ...activeCriteria,
                        eventType: checked === true,
                      })
                    }
                    data-testid="eventType-criteria-checkbox"
                  />
                  <CriteriaCheckbox
                    id="industry"
                    label="Industry"
                    description="Leads are assigned based on the industry of the organization hosting the meeting or event. Ex. SIC code lookup"
                    checked={activeCriteria.industry}
                    onCheckedChange={(checked) =>
                      setActiveCriteria({
                        ...activeCriteria,
                        industry: checked === true,
                      })
                    }
                    data-testid="industry-criteria-checkbox"
                  />
                  <CriteriaCheckbox
                    id="eventNeeds"
                    label="Event Needs"
                    description="Assign leads based on specific requirements (catering, AV, etc.)"
                    checked={activeCriteria.eventNeeds}
                    onCheckedChange={(checked) =>
                      setActiveCriteria({
                        ...activeCriteria,
                        eventNeeds: checked === true,
                      })
                    }
                  />
                  <CriteriaCheckbox
                    id="dayOfMonth"
                    label="Day of Month (Odd/Even)"
                    description="Assign leads based on whether the event date falls on an odd or even day"
                    checked={activeCriteria.dayOfMonth}
                    onCheckedChange={(checked) =>
                      setActiveCriteria({
                        ...activeCriteria,
                        dayOfMonth: checked === true,
                      })
                    }
                  />
                </div>
              </div>
              <div className="flex justify-end">
                <Button
                  onClick={() => handleCriteriaSave(activeCriteria)}
                  data-testid="save-criteria-button"
                >
                  Save Criteria Selections
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        )}

        {/* Dialog only renders on client to prevent hydration mismatch */}
        {isClient && (
          <Dialog
            open={showManageDefinitionsModal}
            onOpenChange={setShowManageDefinitionsModal}
          >
            <DialogContent
              className="sm:max-w-[400px]"
              aria-describedby="manage-definitions-desc"
              data-testid="manage-definitions-modal"
            >
              <DialogHeader>
                <DialogTitle>Manage Definitions</DialogTitle>
              </DialogHeader>
              <div className="py-4">
                <p
                  className="text-sm text-muted-foreground mb-4"
                  id="manage-definitions-desc"
                >
                  Select which definitions you would like to manage.
                </p>
                <div className="space-y-4">
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() =>
                      handleManageDefinitionsSelection("geography")
                    }
                    disabled={!activeCriteria.geography}
                    data-testid="geography-regions-button"
                  >
                    Geography Regions{" "}
                    {!activeCriteria.geography && "(Not Active)"}
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() =>
                      handleManageDefinitionsSelection("roomCount")
                    }
                    disabled={!activeCriteria.roomCount}
                    data-testid="room-count-buckets-button"
                  >
                    Room Count Buckets{" "}
                    {!activeCriteria.roomCount && "(Not Active)"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}

        <div className="flex-1 flex justify-end">
          <div className="flex flex-col sm:flex-row gap-2">
            {/* Validate Button */}
            <Button
              variant="outline"
              onClick={handleCheckIssues}
              disabled={isLoadingValidation}
              data-testid="check-issues-button"
              className="w-full sm:w-auto"
            >
              {isLoadingValidation ? (
                "Validating..."
              ) : (
                <>
                  <AlertTriangleIcon className="h-4 w-4 mr-2" />
                  Validate Rules
                </>
              )}
            </Button>

            <Button
              variant="outline"
              data-testid="assignment-summary"
              onClick={() => {
                // Check if there are overlapping rules from the latest validation
                if (overlappingRules.length > 0) {
                  // Show error message when overlaps exist
                  setShowOverlapAlert(true);
                } else {
                  setShowAssignmentSummary(true);
                }
              }}
            >
              View Assignment Summary
            </Button>
            <Button
              variant="default"
              disabled={
                isLoadingValidation ||
                overlappingRules.length > 0 ||
                individualsWithBlankCriteria.length > 0 ||
                (coverageGaps.length > 0 && !gapsAcknowledged)
              }
              onClick={async () => {
                if (lastValidationTimestamp === null) {
                  setShowValidateAlert(true);
                  return;
                }
                await handleSaveActivate();
              }}
              data-testid="save-activate-button"
              className={`$ {
                lastValidationTimestamp &&
                overlappingRules.length === 0 &&
                (coverageGaps.length === 0 || gapsAcknowledged)
                  ? "bg-green-600 hover:bg-green-700"
                  : ""
              } w-full sm:w-auto`}
            >
              {isLoadingValidation
                ? "Validating..."
                : lastValidationTimestamp &&
                    overlappingRules.length === 0 &&
                    (coverageGaps.length === 0 || gapsAcknowledged)
                  ? "Save & Activate ✓"
                  : "Save & Activate"}
            </Button>
          </div>
        </div>
      </div>

      {/* Assignment Table and Overlap Sampling Warning */}
      {overlapSamplingWarning && (
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-800 p-4 mb-4 rounded">
          <strong>Warning:</strong> The rule set is so complex that overlap
          detection is using sampling instead of exhaustive checking. Overlap
          results may not be 100% reliable. (In the future, we could run a full
          check in the background.)
        </div>
      )}

      {/* Debug info for overlap detection */}
      {validationResultV2 && (
        <div className="text-xs text-gray-500 mb-2">
          Validation state: {validationResultV2.entityIdsWithConflicts.size}{" "}
          conflicts,
          {validationResultV2.entityIdsWithRedundanciesOnly.size} redundancies
          only
        </div>
      )}

      <AssignmentTable
        individuals={convertToV2Format(individuals, teams).individualsV2}
        teams={convertToV2Format(individuals, teams).teamsV2}
        activeCriteria={activeCriteria}
        onUpdateIndividual={updateIndividual}
        onRemoveIndividual={removeIndividual}
        onUpdateTeam={updateTeam}
        onRemoveTeam={removeTeam}
        geographyRegions={geographyRegions}
        roomCountRanges={roomCountRanges}
        onConfigureGeography={() =>
          handleManageDefinitionsSelection("geography")
        }
        onConfigureRoomCount={() =>
          handleManageDefinitionsSelection("roomCount")
        }
        onConfigureCriteria={handleConfigureCriteria}
        onValidateRules={handleCheckIssues}
        entityIdsWithConflicts={
          validationResultV2?.entityIdsWithConflicts || new Set()
        }
        entityIdsWithRedundanciesOnly={
          validationResultV2?.entityIdsWithRedundanciesOnly || new Set()
        }
        criteria={{
          geography: {
            active: activeCriteria.geography,
            regions: geographyRegions,
          },
          roomCount: {
            active: activeCriteria.roomCount,
            ranges: roomCountRanges,
          },
          eventType: {
            active: activeCriteria.eventType,
            options: [
              "Meeting",
              "Conference",
              "Wedding",
              "Social Event",
              "Corporate Event",
            ],
          },
          industry: {
            active: activeCriteria.industry,
            options: [
              "Government",
              "Technology",
              "Healthcare",
              "Finance",
              "Education",
              "Manufacturing",
              "Retail",
              "Other",
            ],
          },
          eventNeeds: {
            active: activeCriteria.eventNeeds,
            options: [
              "Catering",
              "AV Equipment",
              "Meeting Space",
              "Accommodations",
              "Transportation",
            ],
          },
          dayOfMonth: {
            active: activeCriteria.dayOfMonth,
            options: ["Odd Days", "Even Days"],
          },
        }}
      />

      {!showAssignmentSummary && (
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleStartTour}
            className="flex items-center gap-1"
            data-testid="tour-button"
          >
            <HelpCircle className="h-4 w-4" />
            Start Tour
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleToggleChecklist}
            className="flex items-center gap-1"
            data-testid="toggle-checklist-button"
          >
            <CheckSquare className="h-4 w-4" />
            {showChecklist ? "Hide Checklist" : "Show Checklist"}
          </Button>
        </div>
      )}

      {/* Validation Panel */}
      <SlideInPanel
        open={showValidationPanel}
        onOpenChange={setShowValidationPanel}
        title="Configuration Validation"
        footerContent={null}
        data-testid="validation-panel"
      >
        <div className="py-4 space-y-4">
          {isLoadingValidation && (
            <div className="flex justify-center py-6">
              <div className="flex flex-col items-center gap-2">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                <p className="text-sm text-muted-foreground">
                  Validating your configuration...
                </p>
              </div>
            </div>
          )}
          {/* Blank Criteria Section - Critical errors that MUST be fixed */}
          {!isLoadingValidation && individualsWithBlankCriteria.length > 0 && (
            <div className="bg-destructive/10 p-4 rounded-lg border border-destructive">
              <div className="flex items-start gap-2">
                <AlertTriangleIcon className="h-5 w-5 text-destructive shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-medium text-destructive">
                    Rules with Unset Criteria
                  </h3>
                  <p className="text-sm text-muted-foreground mb-1">
                    The following rules have unset criteria that must be
                    configured:
                  </p>
                  <div className="bg-destructive/5 p-2 rounded-sm mb-2">
                    <p className="text-xs text-destructive font-medium">
                      All active criteria must be configured for each rule
                      before you can save and activate rules.
                    </p>
                  </div>
                  {validationResultV2?.rulesWithUnsetCriteria ? (
                    <div className="space-y-2 mt-2">
                      {validationResultV2.rulesWithUnsetCriteria.map(
                        (ruleInfo, idx) => (
                          <div key={idx} className="text-sm">
                            <span className="font-medium">
                              {ruleInfo.entityName}
                            </span>
                            {ruleInfo.entityType === "team" && " (Team)"}
                            {" - Rule has unset: "}
                            <span className="text-destructive">
                              {ruleInfo.unsetCriteria.join(", ")}
                            </span>
                          </div>
                        ),
                      )}
                    </div>
                  ) : (
                    <ul className="list-disc list-inside text-sm mt-2">
                      {individualsWithBlankCriteria.map((individualId) => {
                        const individual = individuals.find(
                          (i) => i.id === individualId,
                        );
                        return individual ? (
                          <li key={individual.id}>{individual.name}</li>
                        ) : null;
                      })}
                    </ul>
                  )}
                </div>
              </div>
            </div>
          )}
          {/* Overlapping Rules Section - Critical errors that MUST be fixed */}
          {!isLoadingValidation && overlappingRules.length > 0 && (
            <div className="bg-destructive/10 p-4 rounded-lg border border-destructive">
              <div className="flex items-start gap-2">
                <AlertTriangleIcon className="h-5 w-5 text-destructive shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-medium text-destructive">
                    Conflicting Rules
                  </h3>
                  <p className="text-sm text-muted-foreground mb-1">
                    The following rules overlap and may cause confusion in lead
                    assignment:
                  </p>
                  <div className="bg-destructive/5 p-2 rounded-sm mb-2">
                    <p className="text-xs text-destructive font-medium">
                      These overlaps must be resolved before you can save and
                      activate rules.
                    </p>
                  </div>
                  <div className="space-y-2 mt-2">
                    {richOverlappingRules.length > 0 ? (
                      richOverlappingRules.map((overlap, index) => {
                        const isExpanded = expandedOverlapIndices.has(index);
                        return (
                          <div
                            key={index}
                            className="border border-destructive/20 rounded-md"
                          >
                            <button
                              onClick={() => toggleOverlapExpanded(index)}
                              className="w-full text-left p-3 hover:bg-destructive/5 transition-colors flex items-center justify-between"
                            >
                              <span className="text-sm flex items-center gap-2">
                                {isExpanded ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                                {overlap.summary}
                              </span>
                            </button>
                            {isExpanded && (
                              <div className="px-3 pb-3">
                                <SingleOverlapDetailDisplay
                                  entity1={overlap.entity1}
                                  entity2={overlap.entity2}
                                  overlapDetails={
                                    overlap.details.criteriaDetails
                                  }
                                  activeCriteria={activeCriteria}
                                  geographyRegions={geographyRegions}
                                  roomCountRanges={roomCountRanges}
                                />
                              </div>
                            )}
                          </div>
                        );
                      })
                    ) : (
                      <ul className="list-disc list-inside text-sm">
                        {overlappingRules.map((rule, index) => (
                          <li key={index}>{rule}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Redundancies Section - Non-blocking warnings (V2 only) */}
          {!isLoadingValidation && validationResultV2?.hasRedundancies && (
            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
              <div className="flex items-start gap-2">
                <AlertTriangleIcon className="h-5 w-5 text-yellow-600 shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-medium text-yellow-700">
                    Redundant Rules (Non-blocking)
                  </h3>
                  <p className="text-sm text-yellow-600 mb-1">
                    The following entities have overlapping rules within
                    themselves. This doesn't prevent saving but may indicate
                    unnecessary duplication:
                  </p>
                  <div className="space-y-2 mt-2">
                    {validationResultV2.allOverlaps
                      .filter((o) => o.type === "redundancy")
                      .map((overlap, idx) => {
                        // For redundancies, we only have one entity ID (since it's within the same entity)
                        const entityId = overlap.involvedEntityIds[0];
                        const entity = [...individuals, ...teams].find(
                          (e) => e.id === entityId,
                        );

                        if (!entity) return null;

                        return (
                          <div key={idx} className="text-sm text-yellow-700">
                            <span className="font-medium">{entity.name}</span>
                            {` has redundant rules (Rule ${(overlap.rule1Index ?? 0) + 1} and Rule ${(overlap.rule2Index ?? 0) + 1})`}
                          </div>
                        );
                      })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Coverage Gaps Section - Warnings that can be acknowledged */}
          {!isLoadingValidation && coverageGaps.length > 0 && (
            <div className="bg-warning/10 p-4 rounded-lg border border-warning max-h-[80vh] overflow-y-auto">
              <div className="flex items-start gap-2">
                <AlertTriangleIcon className="h-5 w-5 text-warning shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-medium text-warning">Coverage Gaps</h3>
                  <div
                    className="text-sm text-muted-foreground mt-1 mb-2 p-2 bg-muted/50 rounded-sm"
                    data-testid="coverage-gap-summary"
                  >
                    <p
                      className="font-semibold"
                      data-testid="coverage-gap-count"
                    >
                      Summary: {coverageGaps.length} unassigned lead scenarios
                      identified
                    </p>
                    <p data-testid="coverage-gap-percentage">
                      Approximately{" "}
                      {Math.round(
                        (coverageGaps.length /
                          (coverageGaps.length +
                            individuals.length +
                            teams.length)) *
                          100,
                      )}
                      % of potential leads will go to the General Inbox.
                    </p>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    The following lead types are not covered by any assignment
                    rule:
                  </p>
                  {/* Matrix showing all coverage gaps */}
                  <div className="my-4">
                    <CoverageGapTable
                      coverageGaps={coverageGaps}
                      activeCriteria={activeCriteria}
                      registries={{ geographyRegions, roomCountRanges }}
                    />
                  </div>
                  <div className="bg-warning/5 p-2 rounded-sm mb-2">
                    <p className="text-xs text-warning font-medium">
                      These gaps must be acknowledged before you can save and
                      activate rules. Leads matching these criteria will go to
                      the General Inbox.
                    </p>
                  </div>
                  <div className="mt-4 flex items-center space-x-2">
                    <Checkbox
                      id="acknowledge-gaps"
                      data-testid="acknowledge-gaps-checkbox"
                      checked={gapsAcknowledged}
                      onCheckedChange={(checked) =>
                        setGapsAcknowledged(checked === true)
                      }
                    />
                    <Label
                      htmlFor="acknowledge-gaps"
                      className="text-sm font-medium"
                    >
                      I acknowledge these gaps and understand their impact
                    </Label>
                  </div>
                  <div className="mt-4">
                    <Button
                      variant="default"
                      size="sm"
                      className="w-full"
                      data-testid="acknowledge-gaps-button"
                      disabled={
                        isLoadingValidation ||
                        overlappingRules.length > 0 ||
                        !gapsAcknowledged
                      }
                      onClick={() => {
                        if (gapsAcknowledged && overlappingRules.length === 0) {
                          setShowValidationPanel(false);
                        }
                      }}
                    >
                      {gapsAcknowledged
                        ? "Accept Gaps & Continue"
                        : "Acknowledge Gaps to Continue"}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
          {/* Success message when validation passes */}
          {!isLoadingValidation &&
            overlappingRules.length === 0 &&
            coverageGaps.length === 0 &&
            lastValidationTimestamp !== null && (
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <div className="flex items-start gap-2">
                  <InfoIcon className="h-5 w-5 text-green-600 shrink-0 mt-0.5" />
                  <div>
                    <h3
                      data-testid="no-validation-issues-found"
                      className="font-medium text-green-700"
                    >
                      No Issues Found
                    </h3>
                    <p className="text-sm text-green-600">
                      Your lead assignment configuration looks good! There are
                      no overlapping rules or coverage gaps.
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-4 border-green-200 hover:bg-green-100 hover:text-green-800"
                      onClick={() => setShowValidationPanel(false)}
                    >
                      Close
                    </Button>
                  </div>
                </div>
              </div>
            )}
          {/* If gaps have been acknowledged but no overlaps */}
          {!isLoadingValidation &&
            overlappingRules.length === 0 &&
            coverageGaps.length > 0 &&
            gapsAcknowledged && (
              <div className="bg-green-50 p-4 rounded-lg border border-green-200 mt-4">
                <div className="flex items-start gap-2">
                  <InfoIcon className="h-5 w-5 text-green-600 shrink-0 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-green-700">
                      Ready to Save & Activate
                    </h3>
                    <p className="text-sm text-green-600">
                      You've acknowledged the coverage gaps. Your configuration
                      is ready to be saved and activated.
                    </p>
                    <div className="flex gap-2 mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-green-200 hover:bg-green-100 hover:text-green-800"
                        onClick={() => setShowValidationPanel(false)}
                      >
                        Close
                      </Button>
                      <Button
                        variant="default"
                        size="sm"
                        onClick={async () => {
                          setShowValidationPanel(false);
                          return handleSaveActivate();
                        }}
                      >
                        Save & Activate Rules
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
        </div>
      </SlideInPanel>

      {/* Fancy AlertDialog for validation required */}
      <AlertDialog open={showValidateAlert} onOpenChange={setShowValidateAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Validation Required</AlertDialogTitle>
            <AlertDialogDescription>
              Please validate your rules before saving and activating. Click{" "}
              <b>Validate Rules</b> first.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              onClick={() => setShowValidateAlert(false)}
              autoFocus
            >
              Close
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Fancy AlertDialog for successful activation */}
      <AlertDialog open={showSuccessAlert} onOpenChange={setShowSuccessAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Rules Successfully Activated!</AlertDialogTitle>
            <AlertDialogDescription>
              Your lead assignment rules have been saved and activated. All
              future leads will be routed according to your configuration.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              onClick={() => setShowSuccessAlert(false)}
              autoFocus
            >
              Close
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Fancy AlertDialog for overlap validation */}
      <AlertDialog open={showOverlapAlert} onOpenChange={setShowOverlapAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cannot View Assignment Summary</AlertDialogTitle>
            <AlertDialogDescription>
              You have overlapping assignment rules that must be resolved before
              viewing the lead assignment summary. Please fix the overlaps
              first.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              onClick={() => setShowOverlapAlert(false)}
              autoFocus
            >
              Close
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* All modals render only on client-side to prevent hydration mismatches */}
      {isClient && (
        <>
          {/* Add Individual Modal */}
          <IndividualDetailsModal
            open={showAddIndividualModal}
            onOpenChange={(open) => {
              setShowAddIndividualModal(open);
              // Clear draft data when modal is closed
              if (!open) {
                setDraftIndividual(null);
              }
            }}
            individual={draftIndividual as Individual | null}
            onSave={handleSaveNewIndividual}
            isEditing={false}
            isCreating={true}
            teams={teams}
            onCreateTeam={(formData) => {
              // Save the current form state to the draftIndividual state
              setDraftIndividual(formData);
              setShowAddIndividualModal(false);
              // Use setTimeout to open team modal after individual modal closes
              setTimeout(() => {
                setShowAddTeamModal(true);
              }, 50);
            }}
          />

          <ConfigureTeamModal
            open={showAddTeamModal}
            onOpenChange={setShowAddTeamModal}
            team={null}
            onSave={handleSaveNewTeam}
            criteria={{
              geography: {
                active: activeCriteria.geography,
                regions: geographyRegions,
              },
              roomCount: {
                active: activeCriteria.roomCount,
                ranges: roomCountRanges,
              },
              eventType: {
                active: activeCriteria.eventType,
                options: [
                  "Meeting",
                  "Conference",
                  "Wedding",
                  "Social Event",
                  "Corporate Event",
                ],
              },
              industry: {
                active: activeCriteria.industry,
                options: [
                  "Government",
                  "Technology",
                  "Healthcare",
                  "Finance",
                  "Education",
                  "Manufacturing",
                  "Retail",
                  "Other",
                ],
              },
              eventNeeds: {
                active: activeCriteria.eventNeeds,
                options: [
                  "Catering",
                  "AV Equipment",
                  "Meeting Space",
                  "Accommodations",
                  "Transportation",
                ],
              },
              dayOfMonth: {
                active: activeCriteria.dayOfMonth,
                options: ["Odd Days", "Even Days"],
              },
            }}
            isCreating={true}
          />

          {/* Geography Definition Modal - For selecting regions for assignment rules */}
          <GeographyDefinitionModal
            open={showGeographyModal}
            onOpenChange={setShowGeographyModal}
            selectedCriterion={
              undefined
            } /* This is intentionally undefined as it's only used for initial setup */
            onSave={(regions) => {
              // This is only used during initial setup, when we've just activated geography criteria
              console.log("Geography regions saved:", regions);
              setShowGeographyModal(false);
            }}
            geographyRegions={geographyRegions}
            onManageDefinitions={() => {
              setShowGeographyModal(false);
              // Use setTimeout to open management modal after selection modal closes
              setTimeout(() => {
                setShowManageGeographyModal(true);
              }, 50);
            }}
          />

          {/* Manage Geography Regions Modal - For defining the available regions */}
          <ManageGeographyRegionsModal
            open={showManageGeographyModal}
            onOpenChange={setShowManageGeographyModal}
            geographyRegions={geographyRegions}
            onSave={handleSaveGeographyRegions}
            isRegionInUse={isGeographyRegionInUse}
            onDefinitionsChanged={cleanupInvalidGeographyValues}
          />

          {/* Room Count Definition Modal */}
          <RoomCountDefinitionModal
            open={showRoomCountModal}
            onOpenChange={setShowRoomCountModal}
            ranges={roomCountRanges}
            onSave={(ranges) => {
              console.log("Room count ranges saved:", ranges);
              setRoomCountRanges(ranges);
              setShowRoomCountModal(false);
            }}
            isRangeInUse={isRoomCountRangeInUse}
            onDefinitionsChanged={cleanupInvalidRoomCountValues}
          />
        </>
      )}

      {/* Assignment Summary Panel */}
      <SlideInPanel
        open={showAssignmentSummary}
        onOpenChange={setShowAssignmentSummary}
        title="Assignment Summary"
        maxContentWidth="4xl"
      >
        <AssignmentSummary
          individuals={individuals}
          registries={{ geographyRegions, roomCountRanges }}
          activeCriteria={activeCriteria}
        />
      </SlideInPanel>
    </div>
  );
}
