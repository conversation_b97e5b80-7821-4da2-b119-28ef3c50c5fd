import type React from "react";
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { Individual, Team } from "./types/lead-assignment";

interface IndividualDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  individual?: Individual | null;
  onSave: (individual: Individual) => void;
  isEditing: boolean;
  isCreating: boolean;
  teams?: Team[];
  onCreateTeam?: (formData: Partial<Individual>) => void;
}

export default function IndividualDetailsModal({
  open,
  onOpenChange,
  individual,
  onSave,
  isEditing,
  isCreating,
  teams = [],
  onCreateTeam,
}: IndividualDetailsModalProps) {
  const [name, setName] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [title, setTitle] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [teamId, setTeamId] = useState<string | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Update form fields when individual or open state changes
  useEffect(() => {
    if (open && individual) {
      console.log("Loading individual data:", individual);
      setName(individual.name || "");
      setFirstName(individual.firstName || "");
      setLastName(individual.lastName || "");
      setTitle(individual.title || "");
      setEmail(individual.email || "");
      setPhone(individual.phone || "");
      setTeamId(individual.teamId || null);
      setErrors({});
    } else if (open && isCreating) {
      // Clear form for new individual
      setName("");
      setFirstName("");
      setLastName("");
      setTitle("");
      setEmail("");
      setPhone("");
      setTeamId(null);
      setErrors({});
    }
  }, [open, individual, isCreating]);

  const validate = () => {
    const newErrors: Record<string, string> = {};

    if (!firstName.trim()) newErrors.firstName = "First name is required";
    if (!lastName.trim()) newErrors.lastName = "Last name is required";
    if (!title.trim()) newErrors.title = "Title is required";
    if (!email.trim()) newErrors.email = "Email is required";
    if (!phone.trim()) newErrors.phone = "Phone is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    console.log("Validating form...");
    if (!validate()) {
      console.log("Validation failed:", errors);
      return;
    }

    console.log("Validation passed, saving individual");
    const updatedIndividual: Individual = {
      ...(individual || {
        id: crypto.randomUUID(),
        criteria: {
          geography: null,
          roomCount: null,
          eventType: null,
          industry: null,
          eventNeeds: null,
          dayOfMonth: null,
        },
        exceptions: [],
        rules: [
          {
            id: crypto.randomUUID(),
            criteria: {
              geography: null,
              roomCount: null,
              eventType: null,
              industry: null,
              eventNeeds: null,
              dayOfMonth: null,
            },
          },
        ],
      }),
      name: `${firstName} ${lastName}`.trim(),
      firstName,
      lastName,
      title,
      email,
      phone,
      teamId,
      criteria: {
        geography: individual?.criteria?.geography || null,
        roomCount: individual?.criteria?.roomCount || null,
        eventType: individual?.criteria?.eventType || null,
        industry: individual?.criteria?.industry || null,
        eventNeeds: individual?.criteria?.eventNeeds || null,
        dayOfMonth: individual?.criteria?.dayOfMonth || null,
      },
      rules: individual?.rules || [
        {
          id: crypto.randomUUID(),
          criteria: {
            geography: null,
            roomCount: null,
            eventType: null,
            industry: null,
            eventNeeds: null,
            dayOfMonth: null,
          },
        },
      ],
    };

    onSave(updatedIndividual);
    onOpenChange(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (isEditing || isCreating)) {
      e.preventDefault();
      console.log("Enter key pressed, attempting to save");
      handleSave();
    }
  };

  const getTitle = () => {
    if (isCreating) return "Add New Individual";
    if (isEditing) return "Edit Individual";
    return "Individual Details";
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                data-testid="individual-first-name-input"
                value={firstName}
                onChange={(e) => {
                  setFirstName(e.target.value);
                  if (errors.firstName) {
                    setErrors({ ...errors, firstName: "" });
                  }
                }}
                disabled={!isEditing && !isCreating}
                onKeyDown={handleKeyDown}
                className={errors.firstName ? "border-red-500" : ""}
              />
              {errors.firstName && (
                <p className="text-red-500 text-sm">{errors.firstName}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                data-testid="individual-last-name-input"
                value={lastName}
                onChange={(e) => {
                  setLastName(e.target.value);
                  if (errors.lastName) {
                    setErrors({ ...errors, lastName: "" });
                  }
                }}
                disabled={!isEditing && !isCreating}
                onKeyDown={handleKeyDown}
                className={errors.lastName ? "border-red-500" : ""}
              />
              {errors.lastName && (
                <p className="text-red-500 text-sm">{errors.lastName}</p>
              )}
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              data-testid="individual-title-input"
              value={title}
              onChange={(e) => {
                setTitle(e.target.value);
                if (errors.title) {
                  setErrors({ ...errors, title: "" });
                }
              }}
              disabled={!isEditing && !isCreating}
              onKeyDown={handleKeyDown}
              className={errors.title ? "border-red-500" : ""}
            />
            {errors.title && (
              <p className="text-red-500 text-sm">{errors.title}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              data-testid="individual-email-input"
              type="email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                if (errors.email) {
                  setErrors({ ...errors, email: "" });
                }
              }}
              disabled={!isEditing && !isCreating}
              onKeyDown={handleKeyDown}
              className={errors.email ? "border-red-500" : ""}
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              data-testid="individual-phone-input"
              value={phone}
              onChange={(e) => {
                setPhone(e.target.value);
                if (errors.phone) {
                  setErrors({ ...errors, phone: "" });
                }
              }}
              disabled={!isEditing && !isCreating}
              onKeyDown={handleKeyDown}
              className={errors.phone ? "border-red-500" : ""}
            />
            {errors.phone && (
              <p className="text-red-500 text-sm">{errors.phone}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="team">Assign to Team</Label>
            <Select
              value={teamId || ""}
              onValueChange={(value) => setTeamId(value === "" ? null : value)}
              disabled={!isEditing && !isCreating}
            >
              <SelectTrigger id="team" data-testid="individual-team-select">
                <SelectValue placeholder="-- No Team (Assign Directly) --" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="no-team">
                  -- No Team (Assign Directly) --
                </SelectItem>
                {teams.map((team) => (
                  <SelectItem key={team.id} value={team.id}>
                    {team.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {(isEditing || isCreating) && onCreateTeam && (
              <Button
                variant="link"
                className="p-0 h-auto text-sm mt-1"
                data-testid="create-new-team-button"
                onClick={(e) => {
                  e.preventDefault();
                  // Create a partial Individual object with current form data
                  const formData: Partial<Individual> = {
                    firstName,
                    lastName,
                    title,
                    email,
                    phone,
                    teamId,
                  };
                  onCreateTeam(formData);
                }}
              >
                + Create New Team
              </Button>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            data-testid="individual-cancel-button"
          >
            {isEditing || isCreating ? "Cancel" : "Close"}
          </Button>
          {(isEditing || isCreating) && (
            <Button onClick={handleSave} data-testid="individual-save-button">
              Save
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
