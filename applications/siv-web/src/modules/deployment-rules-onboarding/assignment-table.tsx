"use client";

import { useState, Fragment, useMemo } from "react";
import { Table, TableBody } from "@/components/ui/table";
import { Plus } from "lucide-react";
import type {
  Individual,
  Team,
  CriteriaTypeString,
  CriterionValue,
  AssignmentRule,
} from "./types/lead-assignment";
import { GeographyDefinitionModal } from "./edit-geography-rule-criteria-modal";
import IndividualDetailsModal from "./individual-details-modal";
import CriteriaSelectionModal from "./criteria-selection-modal";
import ConfigureTeamModal from "./configure-team-modal";
import { ConfirmationDialog } from "./components/confirmation-dialog";
import {
  getTeamMembers,
  getStandaloneIndividuals,
} from "./utils/entity-helpers";
import { TeamSectionV2 } from "./components/team-section";
import { IndividualRowV2 } from "./components/individual-row";
import { AssignmentTableHeader } from "./components/table-header";
import {
  doRulesOverlap,
  findOverlappingAssignments,
} from "./utils/overlap-detection";
import {
  getCriteriaValues,
  toEntityWithCriteria,
} from "./utils/criteria-helpers";
import { getEffectiveCriteria } from "@/modules/deployment-rules-onboarding/utils/rule-evaluation-core";
import { Button } from "@/components/ui/button";

interface AssignmentTableV2Props {
  individuals: Individual[];
  teams: Team[];
  activeCriteria: Partial<Record<CriteriaTypeString, boolean>>;
  onUpdateIndividual: (individual: Individual) => void;
  onRemoveIndividual: (id: string) => void;
  onUpdateTeam: (team: Team) => void;
  onRemoveTeam: (id: string) => void;
  geographyRegions?: any[];
  roomCountRanges?: any[];
  onConfigureGeography?: () => void;
  onConfigureRoomCount?: () => void;
  onConfigureCriteria?: () => void;
  onValidateRules?: () => void;
  entityIdsWithConflicts?: Set<string>;
  entityIdsWithRedundanciesOnly?: Set<string>;
  criteria: any;
}

// Types for modal state
type ModalType =
  | "criteria"
  | "geography"
  | "individualDetails"
  | "teamConfig"
  | "deleteConfirmation"
  | "addRule"
  | "noCriteriaWarning"
  | "emptyCriteriaWarning"
  | null;

interface ModalState {
  type: ModalType;
  entityType: "individual" | "team" | null;
  entityId: string | null;
  ruleId?: string | null;
  criteriaType: CriteriaTypeString | null;
  individual: Individual | null;
  team: Team | null;
  isEditing: boolean;
  deleteEntityName: string;
}

export default function AssignmentTableV2({
  individuals,
  teams,
  activeCriteria,
  onUpdateIndividual,
  onRemoveIndividual,
  onUpdateTeam,
  onRemoveTeam,
  geographyRegions = [],
  roomCountRanges = [],
  onConfigureGeography,
  onConfigureRoomCount,
  onConfigureCriteria,
  onValidateRules,
  entityIdsWithConflicts = new Set(),
  entityIdsWithRedundanciesOnly = new Set(),
  criteria,
}: AssignmentTableV2Props) {
  // Modal state
  const [modalState, setModalState] = useState<ModalState>({
    type: null,
    entityType: null,
    entityId: null,
    ruleId: null,
    criteriaType: null,
    individual: null,
    team: null,
    isEditing: false,
    deleteEntityName: "",
  });

  // Helper functions for modal management
  const openModal = (
    type: ModalType,
    updates: Partial<Omit<ModalState, "type">> = {},
  ) => {
    setModalState((prev) => ({
      ...prev,
      type,
      ...updates,
    }));
  };

  const closeModal = () => {
    setModalState((prev) => ({
      ...prev,
      type: null,
    }));
  };

  const isModalOpen = (type: ModalType) => modalState.type === type;

  // Add a new rule to an entity
  const handleAddRule = (
    entity: Individual | Team,
    entityType: "individual" | "team",
  ) => {
    // Check if any criteria are active
    const hasActiveCriteria = Object.values(activeCriteria).some(
      (isActive) => isActive === true,
    );

    if (!hasActiveCriteria) {
      // Show validation dialog if no criteria are active
      openModal("noCriteriaWarning", {
        entityType,
        entityId: entity.id,
        individual: entityType === "individual" ? (entity as Individual) : null,
        team: entityType === "team" ? (entity as Team) : null,
      });
    } else {
      // Check if entity has existing rules with no criteria set
      const existingRules = entity.rules || [];
      const hasRuleWithNoCriteria = existingRules.some((rule) => {
        const criteriaValues = Object.values(rule.criteria || {});
        return criteriaValues.every((value) => value === null || value === undefined);
      });

      if (hasRuleWithNoCriteria) {
        // Show warning dialog about empty criteria
        openModal("emptyCriteriaWarning", {
          entityType,
          entityId: entity.id,
          individual: entityType === "individual" ? (entity as Individual) : null,
          team: entityType === "team" ? (entity as Team) : null,
        });
      } else {
        // Show normal add rule dialog
        openModal("addRule", {
          entityType,
          entityId: entity.id,
          individual: entityType === "individual" ? (entity as Individual) : null,
          team: entityType === "team" ? (entity as Team) : null,
        });
      }
    }
  };

  // Edit a specific rule
  const handleEditRule = (
    entity: Individual | Team,
    entityType: "individual" | "team",
    rule: AssignmentRule,
    criteriaType: CriteriaTypeString,
  ) => {
    const modalType = criteriaType === "geography" ? "geography" : "criteria";

    openModal(modalType, {
      entityType,
      entityId: entity.id,
      ruleId: rule.id,
      criteriaType,
      individual: entityType === "individual" ? (entity as Individual) : null,
      team: entityType === "team" ? (entity as Team) : null,
    });
  };

  // Add a rule and immediately edit a specific criteria (for first rule scenario)
  const handleAddRuleAndEditCriteria = (
    entity: Individual | Team,
    entityType: "individual" | "team",
    criteriaType: CriteriaTypeString,
  ) => {
    // Check if any criteria are active
    const hasActiveCriteria = Object.values(activeCriteria).some(
      (isActive) => isActive === true,
    );

    if (!hasActiveCriteria) {
      // Show validation dialog if no criteria are active
      openModal("noCriteriaWarning", {
        entityType,
        entityId: entity.id,
        individual: entityType === "individual" ? (entity as Individual) : null,
        team: entityType === "team" ? (entity as Team) : null,
      });
      return;
    }

    // Teams can only have one rule
    if (entityType === "team") {
      const team = entity as Team;
      if (team.rules && team.rules.length > 0) {
        // Team already has a rule, don't allow adding another
        return;
      }
    }

    // Create a new rule
    const newRule: AssignmentRule = {
      id: crypto.randomUUID(),
      criteria: {},
    };

    // Initialize empty criteria for each active type
    const activeCriteriaTypes = Object.keys(activeCriteria)
      .filter((key) => activeCriteria[key as CriteriaTypeString])
      .map((key) => key as CriteriaTypeString);

    activeCriteriaTypes.forEach((type) => {
      newRule.criteria[type] = null;
    });

    // Update the entity with the new rule
    if (entityType === "individual") {
      const individual = entity as Individual;
      const updatedIndividual = {
        ...individual,
        rules: [...(individual.rules || []), newRule],
      };
      onUpdateIndividual(updatedIndividual);

      // Immediately open the criteria editor for the new rule
      setTimeout(() => {
        handleEditRule(updatedIndividual, "individual", newRule, criteriaType);
      }, 0);
    } else {
      const team = entity as Team;
      const updatedTeam = {
        ...team,
        rules: [...(team.rules || []), newRule],
      };
      onUpdateTeam(updatedTeam);

      // Immediately open the criteria editor for the new rule
      setTimeout(() => {
        handleEditRule(updatedTeam, "team", newRule, criteriaType);
      }, 0);
    }
  };

  // Delete a rule
  const handleDeleteRule = (
    entity: Individual | Team,
    entityType: "individual" | "team",
    ruleId: string,
  ) => {
    if (entityType === "individual") {
      const individual = entity as Individual;
      const updatedRules = (individual.rules || []).filter(
        (r) => r.id !== ruleId,
      );
      onUpdateIndividual({ ...individual, rules: updatedRules });
    } else {
      const team = entity as Team;
      const updatedRules = (team.rules || []).filter((r) => r.id !== ruleId);
      onUpdateTeam({ ...team, rules: updatedRules });
    }
  };

  // Handle saving a new rule
  const handleSaveNewRule = () => {
    // Get active criteria types
    const activeCriteriaTypes = Object.keys(activeCriteria)
      .filter((key) => activeCriteria[key as CriteriaTypeString])
      .map((key) => key as CriteriaTypeString);

    // Create a new rule with empty criteria for all active types
    const newRule: AssignmentRule = {
      id: crypto.randomUUID(),
      criteria: {},
    };

    // Initialize empty criteria for each active type
    activeCriteriaTypes.forEach((criteriaType) => {
      newRule.criteria[criteriaType] = null;
    });

    if (modalState.entityType === "individual" && modalState.entityId) {
      // Get the fresh individual data from props instead of using stale modal state
      const currentIndividual = individuals.find((i) => i.id === modalState.entityId);
      if (!currentIndividual) return;
      
      const updatedIndividual = {
        ...currentIndividual,
        rules: [...(currentIndividual.rules || []), newRule],
      };
      onUpdateIndividual(updatedIndividual);
    } else if (modalState.entityType === "team" && modalState.entityId) {
      // Get the fresh team data from props instead of using stale modal state
      const currentTeam = teams.find((t) => t.id === modalState.entityId);
      if (!currentTeam) return;
      
      // Teams can only have one rule
      if (currentTeam.rules && currentTeam.rules.length > 0) {
        // Team already has a rule, don't allow adding another
        closeModal();
        return;
      }
      
      const updatedTeam = {
        ...currentTeam,
        rules: [...(currentTeam.rules || []), newRule],
      };
      onUpdateTeam(updatedTeam);
    }
    closeModal();
  };

  // Handle save geography for a rule
  const handleSaveGeography = (criterion: CriterionValue | null) => {
    if (!modalState.ruleId) return;

    if (modalState.entityType === "team" && modalState.entityId) {
      // Get the fresh team data from props instead of using stale modal state
      const currentTeam = teams.find((t) => t.id === modalState.entityId);
      if (!currentTeam) return;
      
      const updatedRules = (currentTeam.rules || []).map((rule) =>
        rule.id === modalState.ruleId
          ? { ...rule, criteria: { ...rule.criteria, geography: criterion } }
          : rule,
      );
      const updatedTeam = { ...currentTeam, rules: updatedRules };
      onUpdateTeam(updatedTeam);
    } else if (
      modalState.entityType === "individual" &&
      modalState.entityId
    ) {
      // Get the fresh individual data from props instead of using stale modal state
      const currentIndividual = individuals.find((i) => i.id === modalState.entityId);
      if (!currentIndividual) return;
      
      const updatedRules = (currentIndividual.rules || []).map((rule) =>
        rule.id === modalState.ruleId
          ? { ...rule, criteria: { ...rule.criteria, geography: criterion } }
          : rule,
      );
      const updatedIndividual = {
        ...currentIndividual,
        rules: updatedRules,
      };
      onUpdateIndividual(updatedIndividual);
    }
    closeModal();
  };

  // Handle save criteria for a rule
  const handleSaveCriteria = (criterion: CriterionValue | null) => {
    if (!modalState.criteriaType || !modalState.ruleId) return;

    if (modalState.entityType === "team" && modalState.entityId) {
      // Get the fresh team data from props instead of using stale modal state
      const currentTeam = teams.find((t) => t.id === modalState.entityId);
      if (!currentTeam) return;
      
      const updatedRules = (currentTeam.rules || []).map((rule) =>
        rule.id === modalState.ruleId
          ? {
              ...rule,
              criteria: {
                ...rule.criteria,
                [modalState.criteriaType!]: criterion,
              },
            }
          : rule,
      );
      const updatedTeam = { ...currentTeam, rules: updatedRules };
      onUpdateTeam(updatedTeam);
    } else if (
      modalState.entityType === "individual" &&
      modalState.entityId
    ) {
      // Get the fresh individual data from props instead of using stale modal state
      const currentIndividual = individuals.find((i) => i.id === modalState.entityId);
      if (!currentIndividual) return;
      
      const updatedRules = (currentIndividual.rules || []).map((rule) =>
        rule.id === modalState.ruleId
          ? {
              ...rule,
              criteria: {
                ...rule.criteria,
                [modalState.criteriaType!]: criterion,
              },
            }
          : rule,
      );
      const updatedIndividual = {
        ...currentIndividual,
        rules: updatedRules,
      };
      onUpdateIndividual(updatedIndividual);
    }
    closeModal();
  };

  // Other existing handlers...
  const handleEditIndividual = (individual: Individual) => {
    openModal("individualDetails", {
      entityType: "individual",
      entityId: individual.id,
      individual,
      isEditing: true,
    });
  };

  const handleEditTeam = (team: Team) => {
    openModal("teamConfig", {
      entityType: "team",
      entityId: team.id,
      team,
    });
  };

  const handleSaveIndividual = (updatedIndividual: Individual) => {
    onUpdateIndividual(updatedIndividual);
    closeModal();
  };

  const handleSaveTeam = (updatedTeam: Team) => {
    onUpdateTeam(updatedTeam);
    closeModal();
  };

  const handleDeleteIndividual = (individual: Individual) => {
    openModal("deleteConfirmation", {
      entityType: "individual",
      entityId: individual.id,
      deleteEntityName: individual.name,
    });
  };

  const handleDeleteTeam = (team: Team) => {
    openModal("deleteConfirmation", {
      entityType: "team",
      entityId: team.id,
      deleteEntityName: team.name,
    });
  };

  const handleConfirmDelete = () => {
    if (modalState.entityType === "individual") {
      onRemoveIndividual(modalState.entityId!);
    } else if (modalState.entityType === "team") {
      onRemoveTeam(modalState.entityId!);
    }
    closeModal();
  };

  const handleManageGeographyDefinitions = () => {
    closeModal();
    if (onConfigureGeography) {
      setTimeout(() => {
        onConfigureGeography();
      }, 50);
    }
  };

  const handleDefineCriteria = () => {
    closeModal();
    if (onConfigureCriteria) {
      setTimeout(() => {
        onConfigureCriteria();
      }, 50);
    }
  };

  const getCriteriaOptions = (criteriaType: string) => {
    switch (criteriaType) {
      case "geography":
        return geographyRegions && geographyRegions.length > 0
          ? geographyRegions.map((region) => ({
              value: region.id,
              label: region.name,
            }))
          : [];
      case "roomCount":
        return roomCountRanges && roomCountRanges.length > 0
          ? roomCountRanges.map((range) => ({
              value: range.id,
              label: range.name,
            }))
          : [];
      case "eventType":
        return [
          "Meeting",
          "Conference",
          "Wedding",
          "Social Event",
          "Corporate Event",
        ];
      case "industry":
        return [
          "Government",
          "Technology",
          "Healthcare",
          "Finance",
          "Education",
          "Manufacturing",
          "Retail",
          "Other",
        ];
      case "eventNeeds":
        return [
          "Catering",
          "AV Equipment",
          "Meeting Space",
          "Accommodations",
          "Transportation",
        ];
      case "dayOfMonth":
        return ["Odd Days", "Even Days"];
      default:
        return [];
    }
  };

  // Compute overlap data within the component using useMemo to prevent infinite re-renders
  const overlapResults = useMemo(() => {
    return findOverlappingAssignments(individuals, teams, activeCriteria);
  }, [individuals, teams, activeCriteria]);
  const { allOverlaps } = overlapResults;

  // Helper function to get overlapping rule IDs for a specific individual
  const getOverlappingRuleIdsForIndividual = useMemo(() => {
    return (individualId: string): Set<string> => {
      const overlappingRuleIds = new Set<string>();

      allOverlaps.forEach((overlap) => {
        if (overlap.involvedEntityIds.includes(individualId)) {
          overlap.overlappingRuleIds.forEach((ruleId) => {
            // Only include rule IDs that belong to this individual
            const individual = individuals.find(
              (ind) => ind.id === individualId,
            );
            if (individual?.rules.some((rule) => rule.id === ruleId)) {
              overlappingRuleIds.add(ruleId);
            }
          });
        }
      });

      return overlappingRuleIds;
    };
  }, [allOverlaps, individuals]);

  // Handle showing overlap details
  const handleShowOverlapDetails = (entity: Individual | Team) => {
    // Navigate to validation panel to show overlap details
    if (onValidateRules) {
      onValidateRules();
    }
  };

  return (
    <div className="border rounded-md">
      <Table className="assignment-table">
        <AssignmentTableHeader
          activeCriteria={activeCriteria}
          onConfigureGeography={onConfigureGeography}
          onConfigureRoomCount={onConfigureRoomCount}
        />
        <TableBody>
          {/* Standalone Individuals first */}
          {getStandaloneIndividuals(individuals).map((individual) => (
            <IndividualRowV2
              key={individual.id}
              individual={individual}
              hasConflict={entityIdsWithConflicts.has(individual.id)}
              hasRedundancyOnly={entityIdsWithRedundanciesOnly.has(
                individual.id,
              )}
              overlappingRuleIds={getOverlappingRuleIdsForIndividual(
                individual.id,
              )}
              activeCriteria={activeCriteria}
              geographyRegions={geographyRegions}
              roomCountRanges={roomCountRanges}
              onAddRule={(individual) =>
                handleAddRule(individual, "individual")
              }
              onAddRuleAndEditCriteria={(individual, criteriaType) =>
                handleAddRuleAndEditCriteria(
                  individual,
                  "individual",
                  criteriaType,
                )
              }
              onEditRule={(individual, ruleId, criteriaType) => {
                const rule = individual.rules?.find((r) => r.id === ruleId);
                if (rule) {
                  if (criteriaType) {
                    // Direct click on a criteria cell
                    handleEditRule(
                      individual,
                      "individual",
                      rule,
                      criteriaType,
                    );
                  } else {
                    // Click on edit button - open first null criteria
                    const firstNullCriteria = Object.entries(
                      rule.criteria,
                    ).find(([_, value]) => value === null);
                    if (firstNullCriteria) {
                      handleEditRule(
                        individual,
                        "individual",
                        rule,
                        firstNullCriteria[0] as CriteriaTypeString,
                      );
                    }
                  }
                }
              }}
              onDeleteRule={(individual, ruleId) =>
                handleDeleteRule(individual, "individual", ruleId)
              }
              onEditIndividual={handleEditIndividual}
              onDeleteIndividual={handleDeleteIndividual}
              onOverlapClick={handleShowOverlapDetails}
            />
          ))}

          {/* Teams and their members after individuals */}
          {teams.map((team) => {
            const teamMembers = getTeamMembers(team.id, individuals);

            return (
              <Fragment key={`team-section-${team.id}`}>
                <TeamSectionV2
                  team={team}
                  members={teamMembers}
                  hasConflict={entityIdsWithConflicts.has(team.id)}
                  hasRedundancyOnly={entityIdsWithRedundanciesOnly.has(team.id)}
                  individualIdsWithConflicts={entityIdsWithConflicts}
                  individualIdsWithRedundanciesOnly={
                    entityIdsWithRedundanciesOnly
                  }
                  getOverlappingRuleIdsForIndividual={
                    getOverlappingRuleIdsForIndividual
                  }
                  activeCriteria={activeCriteria}
                  geographyRegions={geographyRegions}
                  roomCountRanges={roomCountRanges}
                  onAddTeamRule={(team) => handleAddRule(team, "team")}
                  onAddTeamRuleAndEditCriteria={(team, criteriaType) =>
                    handleAddRuleAndEditCriteria(team, "team", criteriaType)
                  }
                  onEditTeamRule={(team, ruleId, criteriaType) => {
                    const rule = team.rules?.find((r) => r.id === ruleId);
                    if (rule) {
                      if (criteriaType) {
                        // Direct click on a criteria cell
                        handleEditRule(team, "team", rule, criteriaType);
                      } else {
                        // Click on edit button - open first null criteria
                        const firstNullCriteria = Object.entries(
                          rule.criteria,
                        ).find(([_, value]) => value === null);
                        if (firstNullCriteria) {
                          handleEditRule(
                            team,
                            "team",
                            rule,
                            firstNullCriteria[0] as CriteriaTypeString,
                          );
                        }
                      }
                    }
                  }}
                  onDeleteTeamRule={(team, ruleId) =>
                    handleDeleteRule(team, "team", ruleId)
                  }
                  onAddIndividualRule={(individual) =>
                    handleAddRule(individual, "individual")
                  }
                  onAddIndividualRuleAndEditCriteria={(
                    individual,
                    criteriaType,
                  ) =>
                    handleAddRuleAndEditCriteria(
                      individual,
                      "individual",
                      criteriaType,
                    )
                  }
                  onEditIndividualRule={(individual, ruleId, criteriaType) => {
                    const rule = individual.rules?.find((r) => r.id === ruleId);
                    if (rule) {
                      if (criteriaType) {
                        // Direct click on a criteria cell
                        handleEditRule(
                          individual,
                          "individual",
                          rule,
                          criteriaType,
                        );
                      } else {
                        // Click on edit button - open first null criteria
                        const firstNullCriteria = Object.entries(
                          rule.criteria,
                        ).find(([_, value]) => value === null);
                        if (firstNullCriteria) {
                          handleEditRule(
                            individual,
                            "individual",
                            rule,
                            firstNullCriteria[0] as CriteriaTypeString,
                          );
                        }
                      }
                    }
                  }}
                  onDeleteIndividualRule={(individual, ruleId) =>
                    handleDeleteRule(individual, "individual", ruleId)
                  }
                  onEditTeam={handleEditTeam}
                  onDeleteTeam={handleDeleteTeam}
                  onEditIndividual={handleEditIndividual}
                  onDeleteIndividual={handleDeleteIndividual}
                  onShowOverlapDetails={handleShowOverlapDetails}
                />
              </Fragment>
            );
          })}
        </TableBody>
      </Table>

      {/* Geography Definition Modal */}
      {isModalOpen("geography") && modalState.ruleId && (
        <GeographyDefinitionModal
          open={isModalOpen("geography")}
          onOpenChange={closeModal}
          selectedCriterion={(() => {
            // Get fresh entity data from props instead of using stale modal state
            const entity =
              modalState.entityType === "team"
                ? teams.find((t) => t.id === modalState.entityId)
                : individuals.find((i) => i.id === modalState.entityId);
            const rule = (entity?.rules || []).find(
              (r) => r.id === modalState.ruleId,
            );
            return rule?.criteria?.geography;
          })()}
          onSave={handleSaveGeography}
          geographyRegions={criteria.geography.regions || geographyRegions}
          onManageDefinitions={handleManageGeographyDefinitions}
        />
      )}

      {/* Criteria Selection Modal */}
      {isModalOpen("criteria") &&
        modalState.criteriaType &&
        modalState.ruleId && (
          <CriteriaSelectionModal
            open={isModalOpen("criteria")}
            onOpenChange={(open) => {
              if (
                !open &&
                modalState.criteriaType === "roomCount" &&
                (!roomCountRanges || roomCountRanges.length === 0) &&
                onConfigureRoomCount
              ) {
                setTimeout(() => {
                  onConfigureRoomCount();
                }, 50);
              }
              closeModal();
            }}
            criteriaType={modalState.criteriaType}
            selectedCriterion={(() => {
              // Get fresh entity data from props instead of using stale modal state
              const entity =
                modalState.entityType === "team"
                  ? teams.find((t) => t.id === modalState.entityId)
                  : individuals.find((i) => i.id === modalState.entityId);
              const rule = (entity?.rules || []).find(
                (r) => r.id === modalState.ruleId,
              );
              return rule?.criteria?.[modalState.criteriaType!];
            })()}
            options={getCriteriaOptions(modalState.criteriaType)}
            onSave={handleSaveCriteria}
            warningMessage={
              modalState.criteriaType === "roomCount" &&
              (!roomCountRanges || roomCountRanges.length === 0)
                ? {
                    title: "No Room Count Buckets Defined",
                    description:
                      "You need to configure room count buckets before you can select them for assignment rules. Click the Configure button below to set them up.",
                  }
                : undefined
            }
          />
        )}

      {/* Add Rule Modal */}
      {isModalOpen("addRule") && (
        <ConfirmationDialog
          open={isModalOpen("addRule")}
          onOpenChange={closeModal}
          title="Add New Rule"
          description={`Add a new assignment rule for ${modalState.entityType === "individual" ? modalState.individual?.name : modalState.team?.name}. The rule will be created with empty criteria that you can configure.`}
          confirmLabel="Add Rule"
          onConfirm={handleSaveNewRule}
          testId={`dialog-add-rule-${modalState.entityType}-${modalState.entityId}`}
        />
      )}

      {/* Team Configuration Modal */}
      <ConfigureTeamModal
        open={isModalOpen("teamConfig")}
        onOpenChange={closeModal}
        team={modalState.team}
        onSave={handleSaveTeam}
        criteria={criteria}
        isCreating={false}
      />

      {/* Individual Details Modal */}
      <IndividualDetailsModal
        open={isModalOpen("individualDetails")}
        onOpenChange={closeModal}
        individual={modalState.individual}
        onSave={handleSaveIndividual}
        isEditing={modalState.isEditing}
        isCreating={false}
        teams={teams}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={isModalOpen("deleteConfirmation")}
        onOpenChange={closeModal}
        title={`Delete ${modalState.entityType === "individual" ? "Person" : "Team"}`}
        description={`Are you sure you want to delete ${modalState.deleteEntityName}? This action cannot be undone.`}
        confirmLabel="Delete"
        onConfirm={handleConfirmDelete}
        variant="destructive"
      />


      {/* No Criteria Warning Dialog */}
      <ConfirmationDialog
        open={isModalOpen("noCriteriaWarning")}
        onOpenChange={closeModal}
        title="No criteria defined"
        description="You must define assignment criteria before adding rules. Please configure at least one criteria type (e.g., geography, room count, event type) to start creating assignment rules."
        confirmLabel="Define Assignment Criteria"
        onConfirm={handleDefineCriteria}
        hideCancel
        testId="dialog-no-criteria-warning"
      />

      {/* Empty Criteria Warning Dialog */}
      <ConfirmationDialog
        open={isModalOpen("emptyCriteriaWarning")}
        onOpenChange={closeModal}
        title="Complete existing rule first"
        description={`Please set at least one criteria value for the existing rule before adding a new rule. This helps prevent confusion and ensures each rule has a clear purpose.`}
        confirmLabel="Got it"
        onConfirm={closeModal}
        hideCancel
        testId="dialog-empty-criteria-warning"
      />
    </div>
  );
}
