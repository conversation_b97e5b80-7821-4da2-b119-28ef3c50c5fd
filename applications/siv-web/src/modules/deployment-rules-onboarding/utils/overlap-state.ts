/**
 * Determines the data-state attribute value based on conflict/redundancy status
 * @param hasConflict - Whether the entity has conflicts
 * @param hasRedundancyOnly - Whether the entity has redundancies only (no conflicts)
 * @returns The data-state value: "conflict", "redundant", or "normal"
 */
export function getOverlapDataState(
  hasConflict: boolean,
  hasRedundancyOnly: boolean
): "conflict" | "redundant" | "normal" {
  if (hasConflict) {
    return "conflict";
  }
  if (hasRedundancyOnly) {
    return "redundant";
  }
  return "normal";
}