import { describe, it, expect } from "vitest";
import {
  validateAssignmentConfiguration,
  getValidationSummary,
  type ValidationConfig,
  type RuleWithUnsetCriteria,
} from "./validation-service";
import type { Individual, Team } from "../types/lead-assignment";

describe("validateAssignmentConfiguration v2", () => {
  const baseConfig: ValidationConfig = {
    individuals: [],
    teams: [],
    activeCriteria: {
      geography: true,
      eventType: true,
      roomCount: false,
      industry: false,
      eventNeeds: false,
      dayOfMonth: false,
    },
    geographyRegions: [
      { id: "usa", name: "United States", countries: ["US"] },
      { id: "canada", name: "Canada", countries: ["CA"] },
    ],
    roomCountRanges: [
      {
        id: "small",
        name: "1-50",
        condition: "range",
        minValue: 1,
        maxValue: 50,
      },
      { id: "large", name: "51+", condition: "greater", minValue: 51 },
    ],
    gapsAcknowledged: false,
  };

  describe("conflict detection", () => {
    it("should detect conflicts between teams", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Specific", values: ["Wedding"] },
              },
            },
          ],
        },
        {
          id: "team2",
          name: "Team B",
          rules: [
            {
              id: "rule2",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: {
                  type: "Specific",
                  values: ["Wedding", "Corporate"],
                },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(false);
      expect(result.hasConflicts).toBe(true);
      expect(result.entityIdsWithConflicts.has("team1")).toBe(true);
      expect(result.entityIdsWithConflicts.has("team2")).toBe(true);
      expect(result.allOverlaps).toHaveLength(1);
      expect(result.allOverlaps[0].type).toBe("conflict");
    });

    it("should mark configuration as invalid when conflicts exist", () => {
      const individuals: Individual[] = [
        {
          id: "ind1",
          name: "John",
          title: "Sales",
          email: "<EMAIL>",
          phone: "************",
          teamId: "team1",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Any" }, // Add eventType to make rule complete
              },
            },
          ],
        },
        {
          id: "ind2",
          name: "Jane",
          title: "Sales",
          email: "<EMAIL>",
          phone: "************",
          teamId: "team2",
          rules: [
            {
              id: "rule2",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Specific", values: ["Wedding"] }, // Add eventType to make rule complete
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, individuals };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(false);
      expect(result.hasConflicts).toBe(true);
    });
  });

  describe("redundancy detection", () => {
    it("should detect redundancies within the same entity", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Specific", values: ["Wedding"] },
              },
            },
            {
              id: "rule2",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Specific", values: ["Wedding"] },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams, gapsAcknowledged: true };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(true); // Redundancies are not blocking
      expect(result.hasRedundancies).toBe(true);
      expect(result.entityIdsWithRedundanciesOnly.has("team1")).toBe(true);
      expect(result.allOverlaps).toHaveLength(1);
      expect(result.allOverlaps[0].type).toBe("redundancy");
    });

    it("should not mark redundancies as invalid", () => {
      const individuals: Individual[] = [
        {
          id: "ind1",
          name: "John",
          title: "Sales",
          email: "<EMAIL>",
          phone: "************",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Any" },
              },
            },
            {
              id: "rule2",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Specific", values: ["Wedding"] },
              },
            },
          ],
        },
      ];

      const config = {
        ...baseConfig,
        individuals,
        gapsAcknowledged: true,
        assignmentStrategy: "individual" as const,
      };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(true);
      expect(result.hasRedundancies).toBe(true);
    });
  });

  describe("coverage gap detection", () => {
    it("should detect coverage gaps", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Specific", values: ["Wedding"] },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(false);
      expect(result.hasCoverageGaps).toBe(true);
      expect(result.coverageGaps.length).toBeGreaterThan(0);
    });

    it("should allow acknowledged gaps", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Specific", values: ["Wedding"] },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams, gapsAcknowledged: true };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(true);
      expect(result.hasCoverageGaps).toBe(true);
    });

    it("should enrich gaps with display values", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      const canadaGap = result.coverageGaps.find((gap) =>
        gap.missingCombination.some((c) => c.value === "canada"),
      );

      expect(canadaGap).toBeDefined();
      expect(
        canadaGap?.missingCombination.find(
          (c) => c.criteriaType === "geography",
        )?.displayValue,
      ).toBe("Canada");
    });
  });

  describe("inactive entity detection", () => {
    it("should detect entities with no rules", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [],
        },
      ];

      const individuals: Individual[] = [
        {
          id: "ind1",
          name: "John",
          title: "Sales",
          email: "<EMAIL>",
          phone: "************",
          rules: [],
        },
      ];

      const config = { ...baseConfig, teams, individuals };
      const result = validateAssignmentConfiguration(config);

      expect(result.hasInactiveEntities).toBe(true);
      expect(result.inactiveEntityIds).toContain("team1");
      expect(result.inactiveEntityIds).toContain("ind1");
    });

    it("should not affect validity for inactive entities", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [],
        },
        {
          id: "team2",
          name: "Team B",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Any" },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(true);
      expect(result.hasInactiveEntities).toBe(true);
    });
  });

  describe("validation summary", () => {
    it("should generate appropriate summary for valid configuration", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Any" },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);
      const summary = getValidationSummary(result);

      expect(summary).toBe("Configuration is valid");
    });

    it("should list all issues in summary", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Any" },
              },
            },
            {
              id: "rule2",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Any" },
              },
            },
          ],
        },
        {
          id: "team2",
          name: "Team B",
          rules: [
            {
              id: "rule3",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Any" },
              },
            },
          ],
        },
        {
          id: "team3",
          name: "Team C",
          rules: [],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);
      const summary = getValidationSummary(result);

      // Since team1 has both conflicts and redundancies, it only shows up in conflicts
      // The summary shows "2 conflicts detected" (team1 rule1 vs team2, team1 rule2 vs team2)
      expect(summary).toContain("conflict");
      expect(summary).toContain("inactive");

      // Check that all types of issues were detected
      expect(result.allOverlaps.some((o) => o.type === "redundancy")).toBe(
        true,
      );
      expect(result.hasConflicts).toBe(true);
      expect(result.hasInactiveEntities).toBe(true);
    });
  });

  describe("complex scenarios", () => {
    it("should correctly prioritize conflicts over redundancies", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Any" },
              },
            },
            {
              id: "rule2",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Any" },
              },
            },
          ],
        },
        {
          id: "team2",
          name: "Team B",
          rules: [
            {
              id: "rule3",
              criteria: {
                geography: { type: "Specific", values: ["usa", "canada"] },
                eventType: { type: "Any" },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.entityIdsWithConflicts.has("team1")).toBe(true);
      expect(result.entityIdsWithRedundanciesOnly.has("team1")).toBe(false);
    });
  });

  describe("unset criteria detection", () => {
    it("should detect rules with null criteria values", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: null, // Unset criteria
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.hasUnsetCriteria).toBe(true);
      expect(result.rulesWithUnsetCriteria).toHaveLength(1);
      expect(result.rulesWithUnsetCriteria[0]).toEqual({
        entityId: "team1",
        entityName: "Team A",
        entityType: "team",
        ruleId: "rule1",
        unsetCriteria: ["eventType"],
      });
      expect(result.isValid).toBe(false); // Should be invalid due to unset criteria
    });

    it("should detect multiple unset criteria in a single rule", () => {
      const individuals: Individual[] = [
        {
          id: "ind1",
          name: "John Doe",
          title: "Sales Rep",
          email: "<EMAIL>",
          phone: "************",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: null, // Unset
                eventType: null, // Unset
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, individuals };
      const result = validateAssignmentConfiguration(config);

      expect(result.hasUnsetCriteria).toBe(true);
      expect(result.rulesWithUnsetCriteria).toHaveLength(1);
      expect(result.rulesWithUnsetCriteria[0].unsetCriteria).toEqual([
        "geography",
        "eventType",
      ]);
      expect(result.isValid).toBe(false);
    });

    it("should not flag undefined criteria as unset", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                // eventType is undefined (not specified), which is OK
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.hasUnsetCriteria).toBe(false);
      expect(result.rulesWithUnsetCriteria).toHaveLength(0);
    });

    it("should check only active criteria for null values", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Any" },
                roomCount: null, // Null but roomCount is not active
              },
            },
          ],
        },
      ];

      const config = {
        ...baseConfig,
        teams,
        activeCriteria: {
          geography: true,
          eventType: true,
          roomCount: false,
          industry: false,
          eventNeeds: false,
          dayOfMonth: false,
        },
      };
      const result = validateAssignmentConfiguration(config);

      expect(result.hasUnsetCriteria).toBe(false);
      expect(result.isValid).toBe(true);
    });

    it("should include unset criteria in validation summary", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: null,
                eventType: { type: "Any" },
              },
            },
            {
              id: "rule2",
              criteria: {
                geography: { type: "Any" },
                eventType: null,
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);
      const summary = getValidationSummary(result);

      expect(result.rulesWithUnsetCriteria).toHaveLength(2);
      expect(summary).toContain("2 rules with unset criteria");
    });

    it("should validate correctly when all criteria are set", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Any" }, // Changed to Any to cover all cases
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.hasUnsetCriteria).toBe(false);
      expect(result.rulesWithUnsetCriteria).toHaveLength(0);
      expect(result.isValid).toBe(true);
    });

    it("should NOT flag individual rules as having unset criteria when they inherit from team", () => {
      // This test reproduces the exact scenario from the user's issue:
      // Angus Box should inherit Industry criterion from Sales team
      const teams: Team[] = [
        {
          id: "sales-team",
          name: "Sales",
          rules: [
            {
              id: "sales-rule",
              criteria: {
                eventType: { type: "Any" },
                industry: { type: "Any" }, // Team has industry rule
              },
            },
          ],
        },
      ];

      const individuals: Individual[] = [
        {
          id: "angus-box",
          name: "Angus Box",
          title: "Team Member",
          email: "<EMAIL>",
          phone: "************",
          teamId: "sales-team",
          rules: [
            {
              id: "angus-rule",
              criteria: {
                eventType: { type: "Specific", values: ["Wedding"] },
                industry: null, // Individual has null industry - should inherit from team
              },
            },
          ],
        },
      ];

      const config = {
        ...baseConfig,
        teams,
        individuals,
        activeCriteria: {
          geography: false,
          eventType: true,
          industry: true,
          roomCount: false,
          eventNeeds: false,
          dayOfMonth: false,
        },
        gapsAcknowledged: true, // Acknowledge gaps since we're only testing unset criteria
      };
      const result = validateAssignmentConfiguration(config);

      // Should NOT have unset criteria because Angus inherits industry from team
      expect(result.hasUnsetCriteria).toBe(false);
      expect(result.rulesWithUnsetCriteria).toHaveLength(0);
      expect(result.isValid).toBe(true);
    });

    it("should flag individual rules as having unset criteria when team also has null for that criteria", () => {
      // This test ensures we still catch truly unset criteria
      const teams: Team[] = [
        {
          id: "sales-team",
          name: "Sales",
          rules: [
            {
              id: "sales-rule",
              criteria: {
                eventType: { type: "Any" },
                industry: null, // Team also has null industry - can't inherit
              },
            },
          ],
        },
      ];

      const individuals: Individual[] = [
        {
          id: "angus-box",
          name: "Angus Box",
          title: "Team Member",
          email: "<EMAIL>",
          phone: "************",
          teamId: "sales-team",
          rules: [
            {
              id: "angus-rule",
              criteria: {
                eventType: { type: "Specific", values: ["Wedding"] },
                industry: null, // Individual has null industry and team can't provide it
              },
            },
          ],
        },
      ];

      const config = {
        ...baseConfig,
        teams,
        individuals,
        activeCriteria: {
          geography: false,
          eventType: true,
          industry: true,
          roomCount: false,
          eventNeeds: false,
          dayOfMonth: false,
        },
      };
      const result = validateAssignmentConfiguration(config);

      // Should have unset criteria because neither individual nor team has industry
      expect(result.hasUnsetCriteria).toBe(true);
      expect(result.rulesWithUnsetCriteria).toHaveLength(2); // Both team and individual should be flagged
      expect(result.isValid).toBe(false);

      // Check that both team and individual are flagged
      const teamUnsetRule = result.rulesWithUnsetCriteria.find(
        (r) => r.entityId === "sales-team",
      );
      const individualUnsetRule = result.rulesWithUnsetCriteria.find(
        (r) => r.entityId === "angus-box",
      );

      expect(teamUnsetRule).toBeDefined();
      expect(teamUnsetRule?.unsetCriteria).toContain("industry");
      expect(individualUnsetRule).toBeDefined();
      expect(individualUnsetRule?.unsetCriteria).toContain("industry");
    });

    it("should handle individuals with no rules who inherit from team", () => {
      // Test case where individual has no rules but inherits from team
      const teams: Team[] = [
        {
          id: "sales-team",
          name: "Sales",
          rules: [
            {
              id: "sales-rule",
              criteria: {
                eventType: { type: "Any" },
                industry: { type: "Any" },
              },
            },
          ],
        },
      ];

      const individuals: Individual[] = [
        {
          id: "team-member",
          name: "Team Member",
          title: "Sales Rep",
          email: "<EMAIL>",
          phone: "************",
          teamId: "sales-team",
          rules: [], // No individual rules - inherits from team
        },
      ];

      const config = {
        ...baseConfig,
        teams,
        individuals,
        activeCriteria: {
          geography: false,
          eventType: true,
          industry: true,
          roomCount: false,
          eventNeeds: false,
          dayOfMonth: false,
        },
      };
      const result = validateAssignmentConfiguration(config);

      // Should NOT have unset criteria because individual inherits complete team rules
      expect(result.hasUnsetCriteria).toBe(false);
      expect(result.rulesWithUnsetCriteria).toHaveLength(0);
      expect(result.isValid).toBe(true);
    });

    it("should handle individuals with no rules who inherit incomplete team rules", () => {
      // Test case where individual has no rules and team rules are incomplete
      const teams: Team[] = [
        {
          id: "sales-team",
          name: "Sales",
          rules: [
            {
              id: "sales-rule",
              criteria: {
                eventType: { type: "Any" },
                industry: null, // Team rule is incomplete
              },
            },
          ],
        },
      ];

      const individuals: Individual[] = [
        {
          id: "team-member",
          name: "Team Member",
          title: "Sales Rep",
          email: "<EMAIL>",
          phone: "************",
          teamId: "sales-team",
          rules: [], // No individual rules - inherits incomplete team rules
        },
      ];

      const config = {
        ...baseConfig,
        teams,
        individuals,
        activeCriteria: {
          geography: false,
          eventType: true,
          industry: true,
          roomCount: false,
          eventNeeds: false,
          dayOfMonth: false,
        },
      };
      const result = validateAssignmentConfiguration(config);

      // Should have unset criteria because individual inherits incomplete team rules
      expect(result.hasUnsetCriteria).toBe(true);
      expect(result.rulesWithUnsetCriteria).toHaveLength(2); // Both team and individual should be flagged
      expect(result.isValid).toBe(false);

      // Check that both team and individual are flagged for industry
      const teamUnsetRule = result.rulesWithUnsetCriteria.find(
        (r) => r.entityId === "sales-team",
      );
      const individualUnsetRule = result.rulesWithUnsetCriteria.find(
        (r) => r.entityId === "team-member",
      );

      expect(teamUnsetRule).toBeDefined();
      expect(teamUnsetRule?.unsetCriteria).toContain("industry");
      expect(individualUnsetRule).toBeDefined();
      expect(individualUnsetRule?.unsetCriteria).toContain("industry");
    });

    it("should reproduce exact user scenario - Angus Box inheriting from Sales team", () => {
      // This test reproduces the EXACT scenario described by the user:
      // "Angus Box has an unset rule for the Industry criterion, but this is incorrect
      // because Angus Box should inherit the Industry criterion rule from the Sales team."
      const teams: Team[] = [
        {
          id: "sales-team",
          name: "Sales",
          rules: [
            {
              id: "sales-rule",
              criteria: {
                eventType: { type: "Any" },
                industry: { type: "Any" }, // Sales team HAS industry rule
              },
            },
          ],
        },
      ];

      const individuals: Individual[] = [
        {
          id: "angus-box",
          name: "Angus Box",
          title: "Team Member",
          email: "<EMAIL>",
          phone: "************",
          teamId: "sales-team",
          rules: [
            {
              id: "angus-rule",
              criteria: {
                eventType: { type: "Specific", values: ["Wedding"] },
                industry: null, // Angus has null industry - should inherit from Sales team
              },
            },
          ],
        },
      ];

      const config = {
        ...baseConfig,
        teams,
        individuals,
        activeCriteria: {
          geography: false,
          eventType: true,
          industry: true,
          roomCount: false,
          eventNeeds: false,
          dayOfMonth: false,
        },
        gapsAcknowledged: true, // Acknowledge gaps since we're only testing unset criteria
      };
      const result = validateAssignmentConfiguration(config);

      // This is the key assertion: Angus should NOT be flagged for unset industry
      // because he inherits it from the Sales team
      expect(result.hasUnsetCriteria).toBe(false);
      expect(result.rulesWithUnsetCriteria).toHaveLength(0);
      expect(result.isValid).toBe(true);

      // Verify that no individual is flagged for unset criteria
      const angusUnsetRule = result.rulesWithUnsetCriteria.find(
        (r) => r.entityId === "angus-box",
      );
      expect(angusUnsetRule).toBeUndefined();
    });
  });
});
