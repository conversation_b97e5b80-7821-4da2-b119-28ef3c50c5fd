import { describe, it, expect } from "vitest";
import {
  isCriteriaValueInUse,
  cleanupInvalidCriteriaValues,
} from "./usage-checks";
import type {
  Individual,
  Team,
  CriterionValue,
} from "../types/lead-assignment";
import { CRITERION_ANY } from "../types/lead-assignment";

// Type guard function
function isSpecificCriterion(
  criterion: CriterionValue,
): criterion is { type: "Specific"; values: string[] } {
  return criterion.type === "Specific";
}

describe("usage-checks utility", () => {
  describe("isCriteriaValueInUse", () => {
    const mockIndividuals: Individual[] = [
      {
        id: "individual1",
        name: "Individual 1",
        firstName: "First",
        lastName: "Individual",
        title: "Sales Representative",
        phone: "************",
        email: "<EMAIL>",
        rules: [
          {
            id: "rule1",
            criteria: {
              geography: { type: "Specific", values: ["region1", "region2"] },
              roomCount: { type: "Specific", values: ["range1"] },
              eventType: { type: "Specific", values: ["Meeting"] },
              industry: CRITERION_ANY,
              eventNeeds: CRITERION_ANY,
              dayOfMonth: CRITERION_ANY,
            },
          },
        ],
      },
      {
        id: "individual2",
        name: "Individual 2",
        firstName: "Second",
        lastName: "Individual",
        title: "Sales Manager",
        phone: "************",
        email: "<EMAIL>",
        rules: [
          {
            id: "rule2",
            criteria: {
              geography: { type: "Specific", values: ["region3"] },
              roomCount: { type: "Specific", values: ["range2", "range3"] },
              eventType: CRITERION_ANY,
              industry: { type: "Specific", values: ["Technology"] },
              eventNeeds: CRITERION_ANY,
              dayOfMonth: CRITERION_ANY,
            },
          },
        ],
      },
    ];

    const mockTeams: Team[] = [
      {
        id: "team1",
        name: "Team 1",
        rules: [
          {
            id: "teamRule1",
            criteria: {
              geography: { type: "Specific", values: ["region4"] },
              roomCount: { type: "Specific", values: ["range4"] },
              eventType: CRITERION_ANY,
              industry: CRITERION_ANY,
              eventNeeds: CRITERION_ANY,
              dayOfMonth: CRITERION_ANY,
            },
          },
        ],
      },
      {
        id: "team2",
        name: "Team 2",
        rules: [
          {
            id: "teamRule2",
            criteria: {
              geography: { type: "Specific", values: ["region5", "region6"] },
              roomCount: CRITERION_ANY,
              eventType: { type: "Specific", values: ["Conference"] },
              industry: CRITERION_ANY,
              eventNeeds: CRITERION_ANY,
              dayOfMonth: CRITERION_ANY,
            },
          },
        ],
      },
    ];

    it("detects when a geography region is in use by an individual", () => {
      expect(
        isCriteriaValueInUse(
          "geography",
          "region1",
          mockIndividuals,
          mockTeams,
        ),
      ).toBe(true);
      expect(
        isCriteriaValueInUse(
          "geography",
          "region2",
          mockIndividuals,
          mockTeams,
        ),
      ).toBe(true);
    });

    it("detects when a geography region is in use by a team", () => {
      expect(
        isCriteriaValueInUse(
          "geography",
          "region4",
          mockIndividuals,
          mockTeams,
        ),
      ).toBe(true);
      expect(
        isCriteriaValueInUse(
          "geography",
          "region5",
          mockIndividuals,
          mockTeams,
        ),
      ).toBe(true);
    });

    it("detects when a geography region is not in use", () => {
      expect(
        isCriteriaValueInUse(
          "geography",
          "nonexistent-region",
          mockIndividuals,
          mockTeams,
        ),
      ).toBe(false);
    });

    it("detects when a room count range is in use by an individual", () => {
      expect(
        isCriteriaValueInUse("roomCount", "range1", mockIndividuals, mockTeams),
      ).toBe(true);
      expect(
        isCriteriaValueInUse("roomCount", "range2", mockIndividuals, mockTeams),
      ).toBe(true);
    });

    it("detects when a room count range is in use by a team", () => {
      expect(
        isCriteriaValueInUse("roomCount", "range4", mockIndividuals, mockTeams),
      ).toBe(true);
    });

    it("detects when a room count range is not in use", () => {
      expect(
        isCriteriaValueInUse(
          "roomCount",
          "nonexistent-range",
          mockIndividuals,
          mockTeams,
        ),
      ).toBe(false);
    });

    it("works with other criteria types", () => {
      expect(
        isCriteriaValueInUse(
          "eventType",
          "Meeting",
          mockIndividuals,
          mockTeams,
        ),
      ).toBe(true);
      expect(
        isCriteriaValueInUse(
          "eventType",
          "Conference",
          mockIndividuals,
          mockTeams,
        ),
      ).toBe(true);
      expect(
        isCriteriaValueInUse(
          "industry",
          "Technology",
          mockIndividuals,
          mockTeams,
        ),
      ).toBe(true);
      expect(
        isCriteriaValueInUse(
          "industry",
          "Healthcare",
          mockIndividuals,
          mockTeams,
        ),
      ).toBe(false);
    });

    it("handles empty criteria arrays", () => {
      expect(
        isCriteriaValueInUse(
          "eventNeeds",
          "anything",
          mockIndividuals,
          mockTeams,
        ),
      ).toBe(false);
    });
  });

  describe("cleanupInvalidCriteriaValues", () => {
    const mockIndividuals: Individual[] = [
      {
        id: "individual1",
        name: "Individual 1",
        firstName: "First",
        lastName: "Individual",
        title: "Sales Representative",
        phone: "************",
        email: "<EMAIL>",
        rules: [
          {
            id: "rule1",
            criteria: {
              geography: {
                type: "Specific",
                values: ["region1", "region2", "deletedRegion1"],
              },
              roomCount: { type: "Specific", values: ["range1", "deletedRange1"] },
              eventType: { type: "Specific", values: ["Meeting"] },
              industry: CRITERION_ANY,
              eventNeeds: CRITERION_ANY,
              dayOfMonth: CRITERION_ANY,
            },
          },
        ],
      },
      {
        id: "individual2",
        name: "Individual 2",
        firstName: "Second",
        lastName: "Individual",
        title: "Sales Manager",
        phone: "************",
        email: "<EMAIL>",
        rules: [
          {
            id: "rule2",
            criteria: {
              geography: {
                type: "Specific",
                values: ["region3", "deletedRegion2"],
              },
              roomCount: {
                type: "Specific",
                values: ["range2", "range3", "deletedRange2"],
              },
              eventType: CRITERION_ANY,
              industry: { type: "Specific", values: ["Technology"] },
              eventNeeds: CRITERION_ANY,
              dayOfMonth: CRITERION_ANY,
            },
          },
        ],
      },
    ];

    const mockTeams: Team[] = [
      {
        id: "team1",
        name: "Team 1",
        rules: [
          {
            id: "teamRule1",
            criteria: {
              geography: {
                type: "Specific",
                values: ["region4", "deletedRegion3"],
              },
              roomCount: { type: "Specific", values: ["range4", "deletedRange3"] },
              eventType: CRITERION_ANY,
              industry: CRITERION_ANY,
              eventNeeds: CRITERION_ANY,
              dayOfMonth: CRITERION_ANY,
            },
          },
        ],
      },
      {
        id: "team2",
        name: "Team 2",
        rules: [
          {
            id: "teamRule2",
            criteria: {
              geography: {
                type: "Specific",
                values: ["region5", "region6", "deletedRegion4"],
              },
              roomCount: { type: "Specific", values: ["deletedRange4"] },
              eventType: { type: "Specific", values: ["Conference"] },
              industry: CRITERION_ANY,
              eventNeeds: CRITERION_ANY,
              dayOfMonth: CRITERION_ANY,
            },
          },
        ],
      },
    ];

    it("removes invalid geography values from individuals and teams", () => {
      const validGeographyRegions = [
        "region1",
        "region2",
        "region3",
        "region4",
        "region5",
        "region6",
      ];
      const { individuals, teams } = cleanupInvalidCriteriaValues(
        "geography",
        validGeographyRegions,
        mockIndividuals,
        mockTeams,
      );

      // Check individuals
      expect(individuals[0]?.rules[0]?.criteria?.geography).toEqual({
        type: "Specific",
        values: ["region1", "region2"],
      });
      expect(isSpecificCriterion(individuals[0]?.rules[0]?.criteria?.geography!)).toBe(
        true,
      );
      expect(
        isSpecificCriterion(individuals[0]?.rules[0]?.criteria?.geography!) &&
          individuals[0]?.rules[0]?.criteria?.geography?.values.includes("deletedRegion1"),
      ).toBe(false);

      expect(individuals[1].rules[0].criteria.geography).toEqual({
        type: "Specific",
        values: ["region3"],
      });
      expect(
        isSpecificCriterion(individuals[1].rules[0].criteria.geography!) &&
          individuals[1].rules[0].criteria.geography.values.includes("deletedRegion2"),
      ).toBe(false);

      // Check teams
      expect(teams[0].rules[0].criteria.geography).toEqual({
        type: "Specific",
        values: ["region4"],
      });
      expect(
        isSpecificCriterion(teams[0].rules[0].criteria.geography!) &&
          teams[0].rules[0].criteria.geography.values.includes("deletedRegion3"),
      ).toBe(false);

      expect(teams[1].rules[0].criteria.geography).toEqual({
        type: "Specific",
        values: ["region5", "region6"],
      });
      expect(
        isSpecificCriterion(teams[1].rules[0].criteria.geography!) &&
          teams[1].rules[0].criteria.geography.values.includes("deletedRegion4"),
      ).toBe(false);
    });

    it("removes invalid room count values from individuals and teams", () => {
      const validRoomCountRanges = ["range1", "range2", "range3", "range4"];
      const { individuals, teams } = cleanupInvalidCriteriaValues(
        "roomCount",
        validRoomCountRanges,
        mockIndividuals,
        mockTeams,
      );

      // Check individuals
      expect(individuals[0].rules[0].criteria.roomCount).toEqual({
        type: "Specific",
        values: ["range1"],
      });
      expect(
        isSpecificCriterion(individuals[0].rules[0].criteria.roomCount!) &&
          individuals[0].rules[0].criteria.roomCount.values.includes("deletedRange1"),
      ).toBe(false);

      expect(individuals[1].rules[0].criteria.roomCount).toEqual({
        type: "Specific",
        values: ["range2", "range3"],
      });
      expect(
        isSpecificCriterion(individuals[1].rules[0].criteria.roomCount!) &&
          individuals[1].rules[0].criteria.roomCount.values.includes("deletedRange2"),
      ).toBe(false);

      // Check teams
      expect(teams[0].rules[0].criteria.roomCount).toEqual({
        type: "Specific",
        values: ["range4"],
      });
      expect(
        isSpecificCriterion(teams[0].rules[0].criteria.roomCount!) &&
          teams[0].rules[0].criteria.roomCount.values.includes("deletedRange3"),
      ).toBe(false);

      // When all values are removed, it should be set to null (blank/not set)
      expect(teams[1].rules[0].criteria.roomCount).toBeNull();
    });

    it("doesn't modify objects when no invalid values exist", () => {
      const validValues = [
        "region1",
        "region2",
        "region3",
        "region4",
        "region5",
        "region6",
        "deletedRegion1",
        "deletedRegion2",
        "deletedRegion3",
        "deletedRegion4",
      ];

      const { individuals, teams } = cleanupInvalidCriteriaValues(
        "geography",
        validValues,
        mockIndividuals,
        mockTeams,
      );

      // Objects should be the same as the original since no values are invalid
      expect(individuals[0]).toBe(mockIndividuals[0]);
      expect(individuals[1]).toBe(mockIndividuals[1]);
      expect(teams[0]).toBe(mockTeams[0]);
      expect(teams[1]).toBe(mockTeams[1]);
    });

    it("preserves all other criteria values when cleaning up", () => {
      const validRoomCountRanges = ["range1", "range2", "range3"];
      const { individuals, teams } = cleanupInvalidCriteriaValues(
        "roomCount",
        validRoomCountRanges,
        mockIndividuals,
        mockTeams,
      );

      // Verify other criteria were unchanged
      expect(individuals[0].rules[0].criteria.geography).toEqual(
        mockIndividuals[0].rules[0].criteria.geography,
      );
      expect(individuals[0].rules[0].criteria.eventType).toEqual(
        mockIndividuals[0].rules[0].criteria.eventType,
      );

      expect(teams[1].rules[0].criteria.geography).toEqual(
        mockTeams[1].rules[0].criteria.geography,
      );
      expect(teams[1].rules[0].criteria.eventType).toEqual(
        mockTeams[1].rules[0].criteria.eventType,
      );
    });
  });

  describe("Multiple rules per entity", () => {
    it("detects value used across multiple rules in same individual", () => {
      const individualsWithMultipleRules: Individual[] = [
        {
          id: "individual1",
          name: "Multi-rule Individual",
          firstName: "Multi",
          lastName: "Rule",
          title: "Sales Representative",
          phone: "************",
          email: "<EMAIL>",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["region1", "region2"] },
                roomCount: { type: "Specific", values: ["range1"] },
                eventType: CRITERION_ANY,
                industry: CRITERION_ANY,
                eventNeeds: CRITERION_ANY,
                dayOfMonth: CRITERION_ANY,
              },
            },
            {
              id: "rule2",
              criteria: {
                geography: { type: "Specific", values: ["region1", "region3"] }, // region1 appears in both rules
                roomCount: { type: "Specific", values: ["range2"] },
                eventType: { type: "Specific", values: ["Conference"] },
                industry: CRITERION_ANY,
                eventNeeds: CRITERION_ANY,
                dayOfMonth: CRITERION_ANY,
              },
            },
          ],
        },
      ];

      // region1 is used in multiple rules of the same individual
      expect(
        isCriteriaValueInUse("geography", "region1", individualsWithMultipleRules, []),
      ).toBe(true);
      
      // region2 is only in first rule
      expect(
        isCriteriaValueInUse("geography", "region2", individualsWithMultipleRules, []),
      ).toBe(true);
      
      // region3 is only in second rule
      expect(
        isCriteriaValueInUse("geography", "region3", individualsWithMultipleRules, []),
      ).toBe(true);
      
      // region4 is not used at all
      expect(
        isCriteriaValueInUse("geography", "region4", individualsWithMultipleRules, []),
      ).toBe(false);
    });

    it("handles cleanup when some rules become empty after removing invalid values", () => {
      const individuals: Individual[] = [
        {
          id: "individual1",
          name: "Test Individual",
          firstName: "Test",
          lastName: "Individual",
          title: "Sales Representative",
          phone: "************",
          email: "<EMAIL>",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["validRegion"] },
                roomCount: { type: "Specific", values: ["invalidRange"] }, // Will be removed
                eventType: CRITERION_ANY,
                industry: CRITERION_ANY,
                eventNeeds: CRITERION_ANY,
                dayOfMonth: CRITERION_ANY,
              },
            },
            {
              id: "rule2",
              criteria: {
                geography: { type: "Specific", values: ["invalidRegion"] }, // Will be removed
                roomCount: { type: "Specific", values: ["invalidRange"] }, // Will be removed
                eventType: CRITERION_ANY,
                industry: CRITERION_ANY,
                eventNeeds: CRITERION_ANY,
                dayOfMonth: CRITERION_ANY,
              },
            },
          ],
        },
      ];

      // Clean up invalid geography values
      const { individuals: cleanedIndividuals } = cleanupInvalidCriteriaValues(
        "geography",
        ["validRegion"],
        individuals,
        [],
      );

      // First rule should still have validRegion
      expect(cleanedIndividuals[0].rules[0].criteria.geography).toEqual({
        type: "Specific",
        values: ["validRegion"],
      });

      // Second rule should have null geography after cleanup
      expect(cleanedIndividuals[0].rules[1].criteria.geography).toBeNull();

      // Clean up room count - both rules should have null
      const { individuals: fullyCleanedIndividuals } = cleanupInvalidCriteriaValues(
        "roomCount",
        [], // No valid room counts
        cleanedIndividuals,
        [],
      );

      expect(fullyCleanedIndividuals[0].rules[0].criteria.roomCount).toBeNull();
      expect(fullyCleanedIndividuals[0].rules[1].criteria.roomCount).toBeNull();
    });

    it("preserves rule IDs during cleanup", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Test Team",
          rules: [
            {
              id: "unique-rule-id-123",
              criteria: {
                geography: { type: "Specific", values: ["region1", "invalidRegion"] },
                roomCount: CRITERION_ANY,
                eventType: CRITERION_ANY,
                industry: CRITERION_ANY,
                eventNeeds: CRITERION_ANY,
                dayOfMonth: CRITERION_ANY,
              },
            },
          ],
        },
      ];

      const { teams: cleanedTeams } = cleanupInvalidCriteriaValues(
        "geography",
        ["region1"],
        [],
        teams,
      );

      // Rule ID should be preserved
      expect(cleanedTeams[0].rules[0].id).toBe("unique-rule-id-123");
      
      // Only valid values should remain
      expect(cleanedTeams[0].rules[0].criteria.geography).toEqual({
        type: "Specific",
        values: ["region1"],
      });
    });
  });

  describe("Empty rules and edge cases", () => {
    it("handles entities with empty rules arrays", () => {
      const entitiesWithEmptyRules: Individual[] = [
        {
          id: "individual1",
          name: "No Rules Individual",
          firstName: "No",
          lastName: "Rules",
          title: "Sales Representative",
          phone: "************",
          email: "<EMAIL>",
          rules: [], // Empty rules array
        },
      ];

      // Should return false for any criteria check
      expect(
        isCriteriaValueInUse("geography", "anyValue", entitiesWithEmptyRules, []),
      ).toBe(false);

      // Cleanup should return the same entity unchanged
      const { individuals } = cleanupInvalidCriteriaValues(
        "geography",
        [],
        entitiesWithEmptyRules,
        [],
      );
      
      expect(individuals[0]).toBe(entitiesWithEmptyRules[0]); // Same reference
      expect(individuals[0].rules).toHaveLength(0);
    });

    it("handles mixed scenarios with some entities having rules and others not", () => {
      const mixedEntities: Individual[] = [
        {
          id: "individual1",
          name: "With Rules",
          firstName: "With",
          lastName: "Rules",
          title: "Sales Manager",
          phone: "************",
          email: "<EMAIL>",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["region1"] },
                roomCount: CRITERION_ANY,
                eventType: CRITERION_ANY,
                industry: CRITERION_ANY,
                eventNeeds: CRITERION_ANY,
                dayOfMonth: CRITERION_ANY,
              },
            },
          ],
        },
        {
          id: "individual2",
          name: "No Rules",
          firstName: "No",
          lastName: "Rules",
          title: "Sales Representative",
          phone: "************",
          email: "<EMAIL>",
          rules: [],
        },
      ];

      // Only the first individual uses region1
      expect(
        isCriteriaValueInUse("geography", "region1", mixedEntities, []),
      ).toBe(true);

      // Cleanup should only affect the first individual
      const { individuals } = cleanupInvalidCriteriaValues(
        "geography",
        [], // No valid regions
        mixedEntities,
        [],
      );

      // First individual's geography should be null
      expect(individuals[0].rules[0].criteria.geography).toBeNull();
      
      // Second individual should remain unchanged (same reference)
      expect(individuals[1]).toBe(mixedEntities[1]);
    });

    it("handles rules with all criteria set to null", () => {
      const entitiesWithNullCriteria: Team[] = [
        {
          id: "team1",
          name: "Null Criteria Team",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: null,
                roomCount: null,
                eventType: null,
                industry: null,
                eventNeeds: null,
                dayOfMonth: null,
              },
            },
          ],
        },
      ];

      // Should return false for any criteria check
      expect(
        isCriteriaValueInUse("geography", "anyValue", [], entitiesWithNullCriteria),
      ).toBe(false);

      // Cleanup should return the same entity unchanged
      const { teams } = cleanupInvalidCriteriaValues(
        "geography",
        ["validRegion"],
        [],
        entitiesWithNullCriteria,
      );
      
      expect(teams[0]).toBe(entitiesWithNullCriteria[0]); // Same reference
    });
  });

  describe("Complex team and individual interactions", () => {
    it("handles teams and individuals with overlapping criteria values", () => {
      const individuals: Individual[] = [
        {
          id: "individual1",
          name: "Team Member",
          firstName: "Team",
          lastName: "Member",
          title: "Sales Representative",
          phone: "************",
          email: "<EMAIL>",
          teamId: "team1",
          rules: [
            {
              id: "individualRule1",
              criteria: {
                geography: { type: "Specific", values: ["region1", "region2"] },
                roomCount: CRITERION_ANY,
                eventType: CRITERION_ANY,
                industry: CRITERION_ANY,
                eventNeeds: CRITERION_ANY,
                dayOfMonth: CRITERION_ANY,
              },
            },
          ],
        },
      ];

      const teams: Team[] = [
        {
          id: "team1",
          name: "Sales Team",
          rules: [
            {
              id: "teamRule1",
              criteria: {
                geography: { type: "Specific", values: ["region1", "region3"] },
                roomCount: { type: "Specific", values: ["range1"] },
                eventType: CRITERION_ANY,
                industry: CRITERION_ANY,
                eventNeeds: CRITERION_ANY,
                dayOfMonth: CRITERION_ANY,
              },
            },
          ],
        },
      ];

      // region1 is used by both individual and team
      expect(isCriteriaValueInUse("geography", "region1", individuals, teams)).toBe(true);
      
      // region2 is only used by individual
      expect(isCriteriaValueInUse("geography", "region2", individuals, teams)).toBe(true);
      
      // region3 is only used by team
      expect(isCriteriaValueInUse("geography", "region3", individuals, teams)).toBe(true);
      
      // range1 is only used by team
      expect(isCriteriaValueInUse("roomCount", "range1", individuals, teams)).toBe(true);
    });

    it("handles cleanup of multiple criteria types in a single pass", () => {
      const individuals: Individual[] = [
        {
          id: "individual1",
          name: "Multi-criteria Individual",
          firstName: "Multi",
          lastName: "Criteria",
          title: "Sales Representative",
          phone: "************",
          email: "<EMAIL>",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["validRegion", "invalidRegion"] },
                roomCount: { type: "Specific", values: ["validRange", "invalidRange"] },
                eventType: { type: "Specific", values: ["Meeting", "InvalidEvent"] },
                industry: CRITERION_ANY,
                eventNeeds: CRITERION_ANY,
                dayOfMonth: CRITERION_ANY,
              },
            },
          ],
        },
      ];

      // First cleanup geography
      let result = cleanupInvalidCriteriaValues(
        "geography",
        ["validRegion"],
        individuals,
        [],
      );

      // Then cleanup roomCount
      result = cleanupInvalidCriteriaValues(
        "roomCount",
        ["validRange"],
        result.individuals,
        result.teams,
      );

      // Finally cleanup eventType
      result = cleanupInvalidCriteriaValues(
        "eventType",
        ["Meeting"],
        result.individuals,
        result.teams,
      );

      const finalIndividual = result.individuals[0];
      
      // All criteria should be cleaned up properly
      expect(finalIndividual.rules[0].criteria.geography).toEqual({
        type: "Specific",
        values: ["validRegion"],
      });
      expect(finalIndividual.rules[0].criteria.roomCount).toEqual({
        type: "Specific",
        values: ["validRange"],
      });
      expect(finalIndividual.rules[0].criteria.eventType).toEqual({
        type: "Specific",
        values: ["Meeting"],
      });
    });
  });

  describe("Performance with large numbers of rules", () => {
    it("efficiently handles entities with many rules", () => {
      // Create an individual with 10 rules
      const manyRules = Array.from({ length: 10 }, (_, i) => ({
        id: `rule${i}`,
        criteria: {
          geography: { type: "Specific" as const, values: [`region${i}`, `region${i + 10}`] },
          roomCount: { type: "Specific" as const, values: [`range${i}`] },
          eventType: i % 2 === 0 ? CRITERION_ANY : { type: "Specific" as const, values: ["Meeting"] },
          industry: CRITERION_ANY,
          eventNeeds: CRITERION_ANY,
          dayOfMonth: CRITERION_ANY,
        },
      }));

      const individualWithManyRules: Individual = {
        id: "individual1",
        name: "Many Rules Individual",
        firstName: "Many",
        lastName: "Rules",
        title: "Sales Manager",
        phone: "************",
        email: "<EMAIL>",
        rules: manyRules,
      };

      // Test that we can find values across all rules
      expect(
        isCriteriaValueInUse("geography", "region5", [individualWithManyRules], []),
      ).toBe(true);
      expect(
        isCriteriaValueInUse("geography", "region15", [individualWithManyRules], []),
      ).toBe(true);
      expect(
        isCriteriaValueInUse("geography", "region99", [individualWithManyRules], []),
      ).toBe(false);

      // Test cleanup performance
      const validRegions = ["region0", "region2", "region4", "region6", "region8"];
      const { individuals } = cleanupInvalidCriteriaValues(
        "geography",
        validRegions,
        [individualWithManyRules],
        [],
      );

      // Each rule should only have valid regions remaining
      individuals[0].rules.forEach((rule, index) => {
        const geoCriterion = rule.criteria.geography;
        if (geoCriterion && geoCriterion.type === "Specific") {
          geoCriterion.values.forEach(value => {
            expect(validRegions).toContain(value);
          });
        }
      });
    });
  });

  describe("Coverage gaps with Any criteria", () => {
    it("should properly handle eventType any as a valid assignment", () => {
      // This test verifies that having 'Any' (empty array) for eventType
      // properly represents that all event types are covered

      const individualWithAnyEventType: Individual = {
        id: "individual1",
        name: "Any Event Type Individual",
        firstName: "Any",
        lastName: "EventType",
        title: "Sales Representative",
        phone: "************",
        email: "<EMAIL>",
        rules: [
          {
            id: "rule1",
            criteria: {
              geography: { type: "Specific", values: ["region1"] }, // Specific geography
              roomCount: { type: "Specific", values: ["range1"] }, // Specific room count
              eventType: CRITERION_ANY, // "Any" event type
              industry: CRITERION_ANY,
              eventNeeds: CRITERION_ANY,
              dayOfMonth: CRITERION_ANY,
            },
          },
        ],
      };

      const individualsWithAny = [individualWithAnyEventType];
      const teams: Team[] = [];

      // Verify that different event types are considered "in use"
      // when an individual has eventType set to "Any" (empty array)
      expect(
        isCriteriaValueInUse("eventType", "Meeting", individualsWithAny, teams),
      ).toBe(false);
      expect(
        isCriteriaValueInUse(
          "eventType",
          "Conference",
          individualsWithAny,
          teams,
        ),
      ).toBe(false);
      expect(
        isCriteriaValueInUse("eventType", "Wedding", individualsWithAny, teams),
      ).toBe(false);

      // When eventType is "Any", it doesn't mean specific values are "in use",
      // but rather that any value would be accepted by the rule
    });

    it("should properly handle geography any and room count any as valid assignments", () => {
      // This test creates a comprehensive setup with geography, room count,
      // and event type all set to "Any" (empty arrays) to verify proper behavior

      const individualWithAllAny: Individual = {
        id: "individual3",
        name: "All Any Individual",
        firstName: "All",
        lastName: "Any",
        title: "Sales Representative",
        phone: "************",
        email: "<EMAIL>",
        rules: [
          {
            id: "rule1",
            criteria: {
              geography: CRITERION_ANY, // Any geography
              roomCount: CRITERION_ANY, // Any room count
              eventType: CRITERION_ANY, // Any event type
              industry: CRITERION_ANY, // Any industry
              eventNeeds: CRITERION_ANY, // Any event needs
              dayOfMonth: CRITERION_ANY, // Any day of month
            },
          },
        ],
      };

      const individualsWithAllAny = [individualWithAllAny];
      const teams: Team[] = [];

      // Specific geography, room count, and event type values should not be reported as "in use"
      // even though the individual will accept any value for these criteria
      expect(
        isCriteriaValueInUse(
          "geography",
          "region1",
          individualsWithAllAny,
          teams,
        ),
      ).toBe(false);
      expect(
        isCriteriaValueInUse(
          "roomCount",
          "range1",
          individualsWithAllAny,
          teams,
        ),
      ).toBe(false);
      expect(
        isCriteriaValueInUse(
          "eventType",
          "Meeting",
          individualsWithAllAny,
          teams,
        ),
      ).toBe(false);
    });
  });
});
