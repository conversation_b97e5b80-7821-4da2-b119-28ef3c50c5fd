import { describe, it, expect } from "vitest";
import { detectCoverageGaps } from "./coverage-gap-detection";
import type { Individual, Team } from "../types/lead-assignment";

describe("detectCoverageGaps v2", () => {
  describe("basic gap detection", () => {
    it("should detect gaps when no rules are defined", () => {
      const individuals: Individual[] = [];
      const teams: Team[] = [];
      const activeCriteria = {
        geography: true,
        eventType: true,
        roomCount: false,
        industry: false,
        eventNeeds: false,
        dayOfMonth: false
      };

      const gaps = detectCoverageGaps(individuals, teams, activeCriteria);

      expect(gaps).toHaveLength(1);
      expect(gaps[0].id).toBe("gap-no-rules");
      expect(gaps[0].description).toBe("No assignment rules defined");
    });

    it("should detect no gaps when all combinations are covered", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Any" },
              },
            },
          ],
        },
      ];

      const activeCriteria = {
        geography: true,
        eventType: true,
        roomCount: false,
        industry: false,
        eventNeeds: false,
        dayOfMonth: false
      };

      const allPossibleValues = {
        geography: new Set(["USA", "Canada"]),
        eventType: new Set(["Wedding", "Corporate"]),
      };

      const gaps = detectCoverageGaps(
        [],
        teams,
        activeCriteria,
        allPossibleValues,
      );

      expect(gaps).toHaveLength(0);
    });

    it("should detect gaps for uncovered combinations", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["USA"] },
                eventType: { type: "Specific", values: ["Wedding"] },
              },
            },
          ],
        },
      ];

      const activeCriteria = {
        geography: true,
        eventType: true,
        roomCount: false,
        industry: false,
        eventNeeds: false,
        dayOfMonth: false
      };

      const allPossibleValues = {
        geography: new Set(["USA", "Canada"]),
        eventType: new Set(["Wedding", "Corporate"]),
      };

      const gaps = detectCoverageGaps(
        [],
        teams,
        activeCriteria,
        allPossibleValues,
      );

      expect(gaps).toHaveLength(3); // Canada-Wedding, USA-Corporate, Canada-Corporate

      const gapDescriptions = gaps.map((g) => g.description).sort();
      expect(gapDescriptions).toContain("geography: USA, eventType: Corporate");
      expect(gapDescriptions).toContain(
        "geography: Canada, eventType: Wedding",
      );
      expect(gapDescriptions).toContain(
        "geography: Canada, eventType: Corporate",
      );
    });

    it("should handle partial coverage with multiple rules", () => {
      const individuals: Individual[] = [
        {
          id: "ind1",
          name: "John",
          title: "Sales Rep",
          email: "<EMAIL>",
          phone: "************",
          rules: [
            {
              id: "ind1-rule1",
              criteria: {
                geography: { type: "Specific", values: ["USA", "Canada"] },
                eventType: { type: "Specific", values: ["Wedding"] },
              },
            },
            {
              id: "ind1-rule2",
              criteria: {
                geography: { type: "Specific", values: ["Mexico"] },
                eventType: { type: "Any" },
              },
            },
          ],
        },
      ];

      const activeCriteria = {
        geography: true,
        eventType: true,
        roomCount: false,
        industry: false,
        eventNeeds: false,
        dayOfMonth: false
      };

      const allPossibleValues = {
        geography: new Set(["USA", "Canada", "Mexico"]),
        eventType: new Set(["Wedding", "Corporate", "Social"]),
      };

      const gaps = detectCoverageGaps(
        individuals,
        [],
        activeCriteria,
        allPossibleValues,
      );

      // Should only have gaps for USA-Corporate, USA-Social, Canada-Corporate, Canada-Social
      expect(gaps).toHaveLength(4);

      const gapDescriptions = gaps.map((g) => g.description).sort();
      expect(gapDescriptions).toContain("geography: USA, eventType: Corporate");
      expect(gapDescriptions).toContain("geography: USA, eventType: Social");
      expect(gapDescriptions).toContain(
        "geography: Canada, eventType: Corporate",
      );
      expect(gapDescriptions).toContain("geography: Canada, eventType: Social");
    });
  });

  describe("criteria handling", () => {
    it("should handle null criteria (not set)", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["USA"] },
                eventType: null, // null means this rule doesn't match anything
              },
            },
          ],
        },
      ];

      const activeCriteria = {
        geography: true,
        eventType: true,
        roomCount: false,
        industry: false,
        eventNeeds: false,
        dayOfMonth: false
      };

      const allPossibleValues = {
        geography: new Set(["USA"]),
        eventType: new Set(["Wedding"]),
      };

      const gaps = detectCoverageGaps(
        [],
        teams,
        activeCriteria,
        allPossibleValues,
      );

      // The rule doesn't match anything because of null eventType
      expect(gaps).toHaveLength(1);
      expect(gaps[0].description).toBe("geography: USA, eventType: Wedding");
    });

    it("should handle undefined criteria (not specified)", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["USA"] },
                // eventType is undefined - should be ignored
              },
            },
          ],
        },
      ];

      const activeCriteria = {
        geography: true,
        eventType: true,
        roomCount: false,
        industry: false,
        eventNeeds: false,
        dayOfMonth: false
      };

      const allPossibleValues = {
        geography: new Set(["USA", "Canada"]),
        eventType: new Set(["Wedding"]),
      };

      const gaps = detectCoverageGaps(
        [],
        teams,
        activeCriteria,
        allPossibleValues,
      );

      // Rule covers USA with any eventType
      expect(gaps).toHaveLength(1);
      expect(gaps[0].description).toBe("geography: Canada, eventType: Wedding");
    });

    it("should handle dayOfMonth criteria with predefined values", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                dayOfMonth: { type: "Specific", values: ["Odd Days"] },
              },
            },
          ],
        },
      ];

      const activeCriteria = {
        geography: false,
        eventType: false,
        roomCount: false,
        industry: false,
        eventNeeds: false,
        dayOfMonth: true
      };

      const gaps = detectCoverageGaps([], teams, activeCriteria);

      expect(gaps).toHaveLength(1);
      expect(gaps[0].description).toBe("dayOfMonth: Even Days");
    });
  });

  describe("automatic value collection", () => {
    it("should collect values from rules when not provided", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["USA"] },
                eventType: {
                  type: "Specific",
                  values: ["Wedding", "Corporate"],
                },
              },
            },
            {
              id: "rule2",
              criteria: {
                geography: { type: "Specific", values: ["Canada"] },
                eventType: { type: "Specific", values: ["Social"] },
              },
            },
          ],
        },
      ];

      const activeCriteria = {
        geography: true,
        eventType: true,
        roomCount: false,
        industry: false,
        eventNeeds: false,
        dayOfMonth: false
      };

      // Don't provide allPossibleValues - should collect from rules
      const gaps = detectCoverageGaps([], teams, activeCriteria);

      // Should detect gaps for: USA-Social, Canada-Wedding, Canada-Corporate
      expect(gaps).toHaveLength(3);

      const gapDescriptions = gaps.map((g) => g.description).sort();
      expect(gapDescriptions).toContain("geography: USA, eventType: Social");
      expect(gapDescriptions).toContain(
        "geography: Canada, eventType: Wedding",
      );
      expect(gapDescriptions).toContain(
        "geography: Canada, eventType: Corporate",
      );
    });
  });

  describe("team inheritance", () => {
    it("should not report gaps for combinations covered by individual + team inheritance", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Catering Team",
          rules: [
            {
              id: "team-rule1",
              criteria: {
                eventNeeds: { type: "Specific", values: ["Accommodations"] },
                // Team rule only specifies eventNeeds, other criteria are undefined
              },
            },
          ],
        },
      ];

      const individuals: Individual[] = [
        {
          id: "ind1",
          name: "Jim Bones",
          title: "Sales Rep",
          email: "<EMAIL>",
          phone: "************",
          teamId: "team1", // Jim belongs to Catering Team
          rules: [
            {
              id: "ind-rule1",
              criteria: {
                eventType: { type: "Specific", values: ["Wedding"] },
                roomCount: { type: "Specific", values: ["Small"] },
              },
            },
          ],
        },
      ];

      const activeCriteria = {
        geography: false,
        eventType: true,
        roomCount: true,
        industry: false,
        eventNeeds: true,
        dayOfMonth: false
      };

      const allPossibleValues = {
        eventType: new Set(["Wedding", "Corporate"]),
        roomCount: new Set(["Small", "Large"]),
        eventNeeds: new Set(["Accommodations", "Catering"]),
      };

      const gaps = detectCoverageGaps(
        individuals,
        teams,
        activeCriteria,
        allPossibleValues,
      );

      // The problem: Team rules are collected separately and applied to ALL scenarios
      // When a team has eventNeeds: Accommodations, it covers ALL combinations with Accommodations
      // This is incorrect - team rules should only apply through individual inheritance
      
      const gapDescriptions = gaps.map((g) => g.description);
      
      // With proper team inheritance:
      // - Jim Bones (Wedding + Small) + Team (Accommodations) = covers Wedding + Small + Accommodations
      // - No one covers Corporate + * + Accommodations or Wedding + Large + Accommodations
      // So we should have 7 gaps total (only Wedding + Small + Accommodations is covered)
      
      // But with the bug, team rule covers ALL Accommodations scenarios
      // So we only see 4 gaps (all Catering scenarios)
      
      // This assertion will FAIL, demonstrating the bug
      expect(gaps.length).toBe(7); // Expected: 7 gaps, Actual: 4 gaps
      
      // Corporate + Large + Accommodations SHOULD be a gap (no individual covers it)
      // but the bug makes the team rule cover it
      const hasCorporateLargeAccommodationsGap = gapDescriptions.some(desc => 
        desc.includes("eventType: Corporate") && 
        desc.includes("roomCount: Large") && 
        desc.includes("eventNeeds: Accommodations")
      );
      
      // This will FAIL - we expect true (it should be a gap) but get false
      expect(hasCorporateLargeAccommodationsGap).toBe(true);
    });
  });

  describe("complex scenarios", () => {
    it("should handle multiple criteria types", () => {
      const individuals: Individual[] = [
        {
          id: "ind1",
          name: "John",
          title: "Sales Rep",
          email: "<EMAIL>",
          phone: "************",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["USA"] },
                eventType: { type: "Specific", values: ["Wedding"] },
                roomCount: { type: "Specific", values: ["1-50"] },
              },
            },
          ],
        },
      ];

      const activeCriteria = {
        geography: true,
        eventType: true,
        roomCount: true,
        industry: false,
        eventNeeds: false,
        dayOfMonth: false
      };

      const allPossibleValues = {
        geography: new Set(["USA"]),
        eventType: new Set(["Wedding", "Corporate"]),
        roomCount: new Set(["1-50", "51-100"]),
      };

      const gaps = detectCoverageGaps(
        individuals,
        [],
        activeCriteria,
        allPossibleValues,
      );

      // Covered: USA-Wedding-1-50
      // Gaps: USA-Wedding-51-100, USA-Corporate-1-50, USA-Corporate-51-100
      expect(gaps).toHaveLength(3);
    });

    it("should handle empty rules arrays", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [], // Empty rules array
        },
      ];

      const individuals: Individual[] = [
        {
          id: "ind1",
          name: "John",
          title: "Sales Rep",
          email: "<EMAIL>",
          phone: "************",
          rules: [], // Empty rules array
        },
      ];

      const activeCriteria = {
        geography: true,
        eventType: false,
        roomCount: false,
        industry: false,
        eventNeeds: false,
        dayOfMonth: false
      };

      const gaps = detectCoverageGaps(individuals, teams, activeCriteria);

      expect(gaps).toHaveLength(1);
      expect(gaps[0].id).toBe("gap-no-rules");
    });
  });
});
