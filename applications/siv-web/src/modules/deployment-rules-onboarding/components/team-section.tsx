"use client";

import { useState } from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Trash2, Edit, Users } from "lucide-react";
import { CriteriaValueDisplay } from "./criteria-value-display";
import { OverlapIndicator } from "./overlap-indicator";
import { IndividualRowV2 } from "./individual-row";
import { getOverlapDataState } from "../utils/overlap-state";
import type {
  Individual,
  Team,
  CriteriaTypeString,
  AssignmentRule,
} from "../types/lead-assignment";

interface TeamSectionV2Props {
  /**
   * The team to display
   */
  team: Team;

  /**
   * Individual members of this team
   */
  members: Individual[];

  /**
   * Whether this team has conflicts
   */
  hasConflict: boolean;

  /**
   * Whether this team has redundancies only
   */
  hasRedundancyOnly: boolean;

  /**
   * IDs of individuals with conflicts
   */
  individualIdsWithConflicts: Set<string>;

  /**
   * IDs of individuals with redundancies only
   */
  individualIdsWithRedundanciesOnly: Set<string>;

  /**
   * Function to get overlapping rule IDs for a specific individual
   */
  getOverlappingRuleIdsForIndividual: (individualId: string) => Set<string>;

  /**
   * Active criteria types to display
   */
  activeCriteria: Partial<Record<CriteriaTypeString, boolean>>;

  /**
   * Optional geography regions for display name resolution
   */
  geographyRegions?: any[];

  /**
   * Optional room count ranges for display name resolution
   */
  roomCountRanges?: any[];

  /**
   * Called when a team rule is added
   */
  onAddTeamRule: (team: Team) => void;

  /**
   * Called when adding team rule and editing criteria immediately
   */
  onAddTeamRuleAndEditCriteria?: (
    team: Team,
    criteriaType: CriteriaTypeString,
  ) => void;

  /**
   * Called when a team rule is edited
   */
  onEditTeamRule: (
    team: Team,
    ruleId: string,
    criteriaType?: CriteriaTypeString,
  ) => void;

  /**
   * Called when a team rule is deleted
   */
  onDeleteTeamRule: (team: Team, ruleId: string) => void;

  /**
   * Called when an individual rule is added
   */
  onAddIndividualRule: (individual: Individual) => void;

  /**
   * Called when adding individual rule and editing criteria immediately
   */
  onAddIndividualRuleAndEditCriteria?: (
    individual: Individual,
    criteriaType: CriteriaTypeString,
  ) => void;

  /**
   * Called when an individual rule is edited
   */
  onEditIndividualRule: (
    individual: Individual,
    ruleId: string,
    criteriaType?: CriteriaTypeString,
  ) => void;

  /**
   * Called when an individual rule is deleted
   */
  onDeleteIndividualRule: (individual: Individual, ruleId: string) => void;

  /**
   * Called when team edit is clicked
   */
  onEditTeam: (team: Team) => void;

  /**
   * Called when team delete is clicked
   */
  onDeleteTeam: (team: Team) => void;

  /**
   * Called when individual edit is clicked
   */
  onEditIndividual: (individual: Individual) => void;

  /**
   * Called when individual delete is clicked
   */
  onDeleteIndividual: (individual: Individual) => void;

  /**
   * Called when overlap indicator is clicked
   */
  onShowOverlapDetails: (entity: Team | Individual) => void;
}

/**
 * A section that displays a team and its members with v2 multiple rules support
 */
export function TeamSectionV2({
  team,
  members,
  hasConflict,
  hasRedundancyOnly,
  individualIdsWithConflicts,
  individualIdsWithRedundanciesOnly,
  getOverlappingRuleIdsForIndividual,
  activeCriteria,
  geographyRegions = [],
  roomCountRanges = [],
  onAddTeamRule,
  onAddTeamRuleAndEditCriteria,
  onEditTeamRule,
  onDeleteTeamRule,
  onAddIndividualRule,
  onAddIndividualRuleAndEditCriteria,
  onEditIndividualRule,
  onDeleteIndividualRule,
  onEditTeam,
  onDeleteTeam,
  onEditIndividual,
  onDeleteIndividual,
  onShowOverlapDetails,
}: TeamSectionV2Props) {
  // Get active criteria types
  const activeCriteriaTypes = Object.keys(activeCriteria)
    .filter((key) => activeCriteria[key as CriteriaTypeString])
    .map((key) => key as CriteriaTypeString);

  // Determine border color based on conflict/redundancy
  const borderColorClass = hasConflict
    ? "border-l-destructive"
    : hasRedundancyOnly
      ? "border-l-yellow-500"
      : "";

  const bgColorClass = hasConflict
    ? "bg-destructive/5"
    : hasRedundancyOnly
      ? "bg-yellow-50/50"
      : "";

  // Check if team has any rules
  const hasRules = team.rules && team.rules.length > 0;
  const hasOverlap = hasConflict || hasRedundancyOnly;

  return (
    <>
      {/* Main Team Row */}
      <TableRow 
        data-row-type="team" 
        data-testid={`team-row-${team.id}`}
        data-state={getOverlapDataState(hasConflict, hasRedundancyOnly)}
      >
        {/* Team name cell */}
        <TableCell
          className={`${bgColorClass} ${hasOverlap ? `border-l-4 ${borderColorClass}` : ""}`}
        >
          <div className="flex items-center gap-2">
            <div className="w-6" />

            <div className="flex-1">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <button
                  type="button"
                  className="font-medium underline cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-primary rounded"
                  onClick={() => onEditTeam(team)}
                  tabIndex={0}
                  aria-label={`Edit team ${team.name}`}
                >
                  <span data-testid={`team-name-${team.id}`}>{team.name}</span>
                </button>
                <span className="text-sm text-muted-foreground">
                  ({members.length} member{members.length !== 1 ? "s" : ""})
                </span>
              </div>
              {hasOverlap && (
                <div className="flex items-center gap-2 mt-1">
                  <OverlapIndicator
                    hasOverlap={hasOverlap}
                    onClick={() => onShowOverlapDetails(team)}
                    className="text-xs"
                  />
                </div>
              )}
            </div>
          </div>
        </TableCell>

        {/* Criteria cells */}
        {!hasRules ? (
          <>
            {activeCriteriaTypes.map((criteriaType) => (
              <TableCell
                key={criteriaType}
                className={`p-2 border ${bgColorClass}`}
                data-column={criteriaType}
              >
                <button
                  type="button"
                  className="w-full min-h-[2.5rem] text-left bg-gray-50 border border-gray-200 hover:bg-gray-100 hover:border-gray-300 rounded px-3 py-2 transition-all cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-primary"
                  data-criteria={criteriaType}
                  onClick={() => {
                    // If we have a handler for adding and editing in one go, use it
                    if (onAddTeamRuleAndEditCriteria) {
                      onAddTeamRuleAndEditCriteria(team, criteriaType);
                    } else {
                      // Otherwise fall back to just adding
                      onAddTeamRule(team);
                    }
                  }}
                  aria-label={`Add ${criteriaType} rule for team ${team.name}`}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500 italic">
                      No team rule
                    </span>
                    <Plus className="h-3 w-3 text-gray-500" />
                  </div>
                </button>
              </TableCell>
            ))}
          </>
        ) : (
          // Single rule - show criteria values inline
          <>
            {activeCriteriaTypes.map((criteriaType) => {
              const criteriaValue = team.rules[0].criteria[criteriaType];

              return (
                <TableCell
                  key={criteriaType}
                  className={`p-2 border ${bgColorClass}`}
                  data-column={criteriaType}
                >
                  {criteriaValue === null || criteriaValue === undefined ? (
                    <button
                      type="button"
                      className="w-full min-h-[2.5rem] text-left bg-gray-50 border border-gray-200 hover:bg-gray-100 hover:border-gray-300 rounded px-3 py-2 transition-all cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-primary"
                      data-criteria={criteriaType}
                      onClick={() =>
                        onEditTeamRule(team, team.rules[0].id, criteriaType)
                      }
                      aria-label={`Edit ${criteriaType} for team ${team.name}`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500 italic">
                          No team rule
                        </span>
                        <Plus className="h-3 w-3 text-gray-500" />
                      </div>
                    </button>
                  ) : (
                    <button
                      type="button"
                      className="w-full min-h-[2.5rem] text-left border border-transparent hover:bg-gray-50 hover:border-gray-200 rounded px-3 py-2 transition-all cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-primary"
                      data-criteria={criteriaType}
                      onClick={() =>
                        onEditTeamRule(team, team.rules[0].id, criteriaType)
                      }
                      aria-label={`Edit ${criteriaType} for team ${team.name}`}
                    >
                      <div className="flex items-center justify-between">
                        <CriteriaValueDisplay
                          criteriaType={criteriaType}
                          values={
                            criteriaValue.type === "Specific"
                              ? criteriaValue.values
                              : []
                          }
                          isAnyCriterion={criteriaValue.type === "Any"}
                          geographyRegions={geographyRegions}
                          roomCountRanges={roomCountRanges}
                        />
                        <Edit className="h-3 w-3 text-gray-400 ml-2" />
                      </div>
                    </button>
                  )}
                </TableCell>
              );
            })}
          </>
        )}

        {/* Actions cell */}
        <TableCell className={bgColorClass}>
          <div className="flex items-center gap-2">
            {/* Only show Add Rule button if team has no rules */}
            {(!team.rules || team.rules.length === 0) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onAddTeamRule(team)}
                className="text-xs"
                data-testid={`add-rule-team-${team.id}`}
              >
                <Plus className="h-3 w-3 mr-1" />
                Add Rule
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDeleteTeam(team)}
              className="text-destructive hover:text-destructive"
              data-testid={`delete-team-${team.id}`}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </TableCell>
      </TableRow>


      {/* Team Members */}
      {members.map((individual) => (
        <IndividualRowV2
          key={`${team.id}-member-${individual.id}`}
          individual={individual}
          hasConflict={individualIdsWithConflicts.has(individual.id)}
          hasRedundancyOnly={individualIdsWithRedundanciesOnly.has(
            individual.id,
          )}
          overlappingRuleIds={getOverlappingRuleIdsForIndividual(individual.id)}
          activeCriteria={activeCriteria}
          geographyRegions={geographyRegions}
          roomCountRanges={roomCountRanges}
          onAddRule={onAddIndividualRule}
          onAddRuleAndEditCriteria={onAddIndividualRuleAndEditCriteria}
          onEditRule={onEditIndividualRule}
          onDeleteRule={onDeleteIndividualRule}
          onEditIndividual={onEditIndividual}
          onDeleteIndividual={onDeleteIndividual}
          onOverlapClick={onShowOverlapDetails}
          team={team}
          inTeamSection={true}
        />
      ))}
    </>
  );
}
