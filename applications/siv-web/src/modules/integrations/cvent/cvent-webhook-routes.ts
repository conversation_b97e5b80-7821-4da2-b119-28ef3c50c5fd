import { Hono } from "hono";
import { HTTPException } from "hono/http-exception";
import logger from "@/logger";
import { Variables } from "@/auth";
import { env } from "@/config/env";

export function createCventRoutes() {
  const app = new Hono<{ Variables: Variables }>().post(
    "/webhook",
    async (c) => {
      // Extract token from Authorization header - Cvent sends it as "authorization: <yourtoken>"
      const authHeader = c.req.header("Authorization");
      
      if (!authHeader || authHeader !== env.CVENT_WEBHOOK_TOKEN) {
        logger.warn("Cvent webhook called with invalid token", {
          hasAuthHeader: !!authHeader,
          userAgent: c.req.header("User-Agent"),
        });
        return c.json({ error: "Invalid token" }, 403);
      }

      try {
        // Get the webhook payload
        const payload = await c.req.json();
        
        // Log the webhook payload
        logger.info("Cvent webhook received", {
          eventType: payload.eventType,
          messageTime: payload.messageTime,
          messageStub: payload.messageStub,
          messageCount: Array.isArray(payload.message) ? payload.message.length : 0,
          userAgent: c.req.header("User-Agent"),
          requestId: c.req.header("request-id"),
        });

        // Return success response
        return c.json({ success: true }, 200);
      } catch (error) {
        logger.error("Error processing Cvent webhook", {
          error: error instanceof Error ? error.message : "Unknown error",
        });
        return c.json({ error: "Internal server error" }, 500);
      }
    }
  );

  return app;
}

export type CventRoutes = ReturnType<typeof createCventRoutes>;