import { beforeEach, describe, expect, it, vi } from "vitest";
import { createCventRoutes, CventRoutes } from "./cvent-webhook-routes";
import { Hono } from "hono";
import { Variables } from "@/auth";
import { mockAuthMiddleware, testRenderer } from "@/test/setup";

const validToken = "test-cvent-webhook-token";

describe("Cvent Webhook Routes", () => {
  let routes: CventRoutes;
  let app: Hono<{ Variables: Variables }, {}, "/">;

  beforeEach(() => {
    app = new Hono<{ Variables: Variables }>();
    app.use("*", mockAuthMiddleware);
    routes = createCventRoutes();
    app.route("/", routes);
  });

  describe("POST /webhook", () => {
    const sampleCventPayload = {
      eventType: "Contact.Created",
      messageTime: "2024-05-08T15:05:03.617Z",
      messageStub: "msg-123456",
      message: [
        {
          firstName: "<PERSON>",
          lastName: "<PERSON>",
          email: "<EMAIL>",
        },
      ],
    };

    it("should return 200 with valid token in Authorization header", async () => {
      const res = await app.request("/webhook", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: validToken,
          "User-Agent": "Cvent-Webhooks",
        },
        body: JSON.stringify(sampleCventPayload),
      });

      expect(res.status).toBe(200);
      const responseBody = await res.json();
      expect(responseBody).toEqual({ success: true });
    });

    it("should return 403 with invalid token", async () => {
      const res = await app.request("/webhook", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "invalid-token",
          "User-Agent": "Cvent-Webhooks",
        },
        body: JSON.stringify(sampleCventPayload),
      });

      expect(res.status).toBe(403);
      const responseBody = await res.json();
      expect(responseBody).toEqual({ error: "Invalid token" });
    });

    it("should return 403 with missing token", async () => {
      const res = await app.request("/webhook", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(sampleCventPayload),
      });

      expect(res.status).toBe(403);
      const responseBody = await res.json();
      expect(responseBody).toEqual({ error: "Invalid token" });
    });

    it("should handle malformed JSON payload", async () => {
      const res = await app.request("/webhook", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: validToken,
          "User-Agent": "Cvent-Webhooks",
        },
        body: "{ invalid json",
      });

      expect(res.status).toBe(500);
      const responseBody = await res.json();
      expect(responseBody).toEqual({ error: "Internal server error" });
    });
  });
});
