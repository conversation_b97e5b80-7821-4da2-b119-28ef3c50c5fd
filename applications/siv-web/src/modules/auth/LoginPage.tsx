import { SignIn } from "@clerk/clerk-react";
import React from "react";
import { SivAdminClerkProvider } from "@/components/siv-admin-clerk-provider";

function LoginPage() {
  return (
    <SivAdminClerkProvider>
      <div className="min-h-screen bg-background flex flex-col">
        {/* Header with Siv branding */}
        <header className="border-b">
          <div className="flex items-center gap-2 px-6 py-4">
            <h2 className="text-2xl font-semibold flex items-center gap-2">
              <span className="inline-flex items-center justify-center bg-accent text-primary text-center w-10 h-10 rounded-full dark font-bold">
                Siv
              </span>
              Admin Portal
            </h2>
          </div>
        </header>

        {/* Main content area with centered sign-in */}
        <main className="flex-1 flex items-center justify-center p-6">
          <div className="w-full max-w-md">
            <SignIn
              appearance={{
                elements: {
                  rootBox: "mx-auto",
                  card: "shadow-none border rounded-lg",
                },
              }}
              signInUrl="/admin/login"
              afterSignInUrl="/admin"
            />
          </div>
        </main>

        {/* Optional footer */}
        <footer className="border-t py-4 px-6 text-center text-sm text-muted-foreground">
          © {new Date().getFullYear()} Siv. All rights reserved.
        </footer>
      </div>
    </SivAdminClerkProvider>
  );
}

export default LoginPage;
