import React from "react";
import { User } from "@/user";
import { AdminRoutes } from "@/modules/admin/admin-routes";
import { hc } from "hono/client";
import { Toaster } from "sonner";
import {
  BriefcaseBusiness,
  Handshake,
  Home,
  Hotel,
  Key,
  LogOut,
  FileText,
} from "lucide-react";
import { SignOutButton } from "@clerk/clerk-react";
import { SivAdminClerkProvider } from "@/components/siv-admin-clerk-provider";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { indexUrl } from "@/modules/route-helpers";

type AdminLayoutProps = {
  children?: React.ReactNode;
  pageTitle: string;
  adminPortalBaseUrl: string;
  pagePath: string;
  user: User;
};

export type AdminLayoutComponentProps = Omit<AdminLayoutProps, "children">;

type AdminSidebarProps = {
  adminPortalBaseUrl: string;
};

const AdminSidebar = ({ adminPortalBaseUrl }: AdminSidebarProps) => {
  const client = hc<AdminRoutes>(adminPortalBaseUrl);

  return (
    <Sidebar>
      <SidebarHeader>
        <div className="flex items-center gap-2 px-4 py-2">
          <h2 className="text-lg font-semibold">
            <span className="inline-block bg-accent text-primary text-center  w-8 h-8  rounded-full dark">
              Siv
            </span>{" "}
            Admin Portal
          </h2>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Overview</SidebarGroupLabel>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <a href={indexUrl(client.index.$url().href)}>
                  <Home size={16} />
                  <span>Home</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <a href={client.properties.$url().href}>
                  <Hotel size={16} />
                  <span>Properties</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <a href={client["management-groups"].$url().href}>
                  <Handshake size={16} />
                  <span>Management Groups</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <div className="mt-auto border-t p-4">
        <SignOutButton redirectUrl="/admin/login">
          <SidebarMenuButton asChild>
            <button className="w-full justify-start">
              <LogOut size={16} />
              <span>Sign Out</span>
            </button>
          </SidebarMenuButton>
        </SignOutButton>
      </div>
    </Sidebar>
  );
};

export const AdminLayout: React.FC<AdminLayoutProps> = (props) => {
  return (
    <SivAdminClerkProvider>
      <SidebarProvider defaultOpen>
        <div className="flex h-screen w-full">
          <AdminSidebar adminPortalBaseUrl={props.adminPortalBaseUrl} />
          <SidebarInset className="w-full">
            <header className="flex h-16 shrink-0 items-center gap-2 border-b">
              <div className="flex items-center gap-2 px-4">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="mr-2 h-4" />
                <h1 className="text-xl font-semibold">{props.pageTitle}</h1>
              </div>
            </header>
            <main className="flex-1 overflow-y-auto px-6 py-4">
              {props.children}
            </main>
          </SidebarInset>
        </div>
        <Toaster />
      </SidebarProvider>
    </SivAdminClerkProvider>
  );
};

export default AdminLayout;
