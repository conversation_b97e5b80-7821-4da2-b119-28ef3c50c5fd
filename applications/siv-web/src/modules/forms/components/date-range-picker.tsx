"use client";

import React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { clientSideDetectIsMobile } from "@/lib/client-mobile-detection";
import { ClientOnly } from "@/components/client-only";

export interface DateRangePickerProps {
  initialDateRange?: [Date | undefined, Date | undefined];
  className?: string;
  startLabel?: string;
  endLabel?: string;
  errors?: Record<string, string[]> & {
    START_DATE?: string[];
    END_DATE?: string[];
    _deduplicated?: string[];
  };
  /** Server's initial guess if device is mobile based on user agent */
  isMobileGuess?: boolean;
  /** Whether the date range is required */
  required?: boolean;
  testModeForceMobile?: boolean;
  /** Callback when the date range changes */
  onChange?: (startDate: Date | undefined, endDate: Date | undefined) => void;
  /** Force the calendar popover to stay open for testing/snapshots */
  testModeForceOpen?: boolean;
  /** Unique ID for the popover (used for styling in stories) */
  popoverId?: string;
}

export function DateRangePicker({
  initialDateRange = [undefined, undefined],
  className,
  startLabel = "Start Date",
  endLabel = "End Date",
  errors = {},
  isMobileGuess = false,
  required = false,
  testModeForceMobile = false,
  onChange,
  testModeForceOpen = false,
  popoverId,
}: DateRangePickerProps) {
  const [dateRange, setDateRange] =
    React.useState<[Date | undefined, Date | undefined]>(initialDateRange);
  const [startDate, endDate] = dateRange;
  const [isOpen, setIsOpen] = React.useState(testModeForceOpen);
  const [isMobile, setIsMobile] = React.useState(isMobileGuess);
  const [hoveredDate, setHoveredDate] = React.useState<Date | undefined>();
  const [isSelecting, setIsSelecting] = React.useState(false);
  const [clickCount, setClickCount] = React.useState(0);

  // Check actual mobile state on mount
  React.useEffect(() => {
    if (typeof window !== "undefined") {
      const actualIsMobile = clientSideDetectIsMobile() || testModeForceMobile;
      // Only update if we got a non-null result and it differs from our current state
      if (actualIsMobile !== null && actualIsMobile !== isMobileGuess) {
        setIsMobile(actualIsMobile);
      }
    }
  }, [isMobileGuess, isMobile]);

  const handleDateChange = (index: number) => (date: Date | undefined) => {
    const newDateRange: [Date | undefined, Date | undefined] = [...dateRange];
    newDateRange[index] = date;
    setDateRange(newDateRange);

    // Call the onChange callback if it exists
    if (onChange) {
      onChange(
        index === 0 ? date : newDateRange[0],
        index === 1 ? date : newDateRange[1],
      );
    }
  };

  const handleCalendarSelect = (
    range: { from?: Date; to?: Date } | undefined,
  ) => {
    if (!range) {
      setDateRange([undefined, undefined]);
      setIsSelecting(false);
      setClickCount(0);
      return;
    }

    const prevStartDate = startDate;
    const prevEndDate = endDate;
    
    setDateRange([range.from, range.to]);

    // If we have only from date, we're starting a selection
    if (range.from && !range.to) {
      setIsSelecting(true);
      setClickCount(1);
    }

    // If we have both dates, selection is complete
    if (range.to) {
      const isAdjustingExistingRange = prevStartDate && prevEndDate && 
        (range.from?.getTime() === prevStartDate.getTime() || range.to?.getTime() === prevEndDate.getTime());
      
      if (isAdjustingExistingRange) {
        // User clicked within existing range to adjust it
        // Keep the calendar open for further adjustments
        setClickCount(0);
        setIsSelecting(false);
      } else {
        // This is a new complete selection
        setIsOpen(false);
        setIsSelecting(false);
        setClickCount(0);
      }
      
      setHoveredDate(undefined);

      // Call onChange callback
      if (onChange) {
        onChange(range.from, range.to);
      }
    }
  };

  // Add a log before button rendering
  const buttonText = startDate
    ? endDate
      ? `${format(startDate, "LLL dd, y")} - ${format(endDate, "LLL dd, y")}`
      : format(startDate, "LLL dd, y")
    : "Pick a date range";

  // Placeholder rendering for server-side that won't have hydration mismatches
  const serverRenderPlaceholder = (
    <Button
      data-testid="date-range-picker-button"
      id="date"
      variant={"outline"}
      className={cn(
        "w-full justify-start text-left font-normal",
        !startDate && !endDate && "text-muted-foreground",
        errors.START_DATE &&
          errors.START_DATE.length > 0 &&
          "!border-destructive",
      )}
    >
      <CalendarIcon className="mr-2 h-4 w-4" />
      {buttonText}
    </Button>
  );

  // Client-side only popover implementation
  const clientSidePopover = (
    <Popover
      open={isOpen}
      onOpenChange={testModeForceOpen ? undefined : (open) => {
        // Don't close if we're in the middle of selecting
        if (!open && isSelecting) {
          return;
        }
        setIsOpen(open);
      }}
    >
      <PopoverTrigger asChild>
        <Button
          data-testid="date-range-picker-button"
          id="date"
          variant={"outline"}
          className={cn(
            "w-full justify-start text-left font-normal",
            !startDate && !endDate && "text-muted-foreground",
            errors.START_DATE &&
              errors.START_DATE.length > 0 &&
              "!border-destructive",
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {buttonText}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start" id={popoverId}>
        <Calendar
          initialFocus
          mode="range"
          defaultMonth={startDate}
          selected={{ from: startDate, to: endDate }}
          onSelect={handleCalendarSelect}
          numberOfMonths={2}
          onDayMouseEnter={(date: Date) => {
            if (isSelecting && startDate) {
              setHoveredDate(date);
            }
          }}
          onDayMouseLeave={() => {
            setHoveredDate(undefined);
          }}
          modifiers={
            isSelecting && hoveredDate && startDate && !endDate
              ? {
                  previewRange: {
                    from: hoveredDate < startDate ? hoveredDate : startDate,
                    to: hoveredDate < startDate ? startDate : hoveredDate,
                  },
                }
              : {}
          }
          modifiersClassNames={{
            previewRange: "!bg-[var(--date-range-bg-light)] opacity-50",
          }}
        />
      </PopoverContent>
    </Popover>
  );

  if (isMobile) {
    return (
      <div className={cn("grid gap-2", className)}>
        <div
          className="grid gap-2"
          data-testid="date-range-picker-EVENT_DATE_RANGE"
        >
          <div className="grid sm:grid-cols-2 gap-4">
            <div>
              <label
                data-testid="start-date-label"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {startLabel}
                {required && "*"}
              </label>
              <input
                data-testid="start-date-input"
                type="date"
                className={cn(
                  "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                  errors.START_DATE &&
                    errors.START_DATE.length > 0 &&
                    "!border-destructive",
                )}
                name="START_DATE_DISPLAY"
                value={startDate ? format(startDate, "yyyy-MM-dd") : ""}
                max={endDate ? format(endDate, "yyyy-MM-dd") : undefined}
                onChange={(e) => {
                  if (!e.target.value) {
                    handleDateChange(0)(undefined);
                    return;
                  }
                  // Parse the date in local timezone by appending the time component
                  const [year, month, day] = e.target.value
                    .split("-")
                    .map(Number);
                  const date = new Date(year, month - 1, day, 12); // Use noon to avoid any timezone issues
                  handleDateChange(0)(date);
                }}
              />
              <input
                type="hidden"
                name="START_DATE"
                value={startDate ? startDate.toISOString() : ""}
              />
              {/* In mobile view, show individual errors */}
              {errors.START_DATE && errors.START_DATE.length > 0 && (
                <p className="text-sm text-destructive mt-1">
                  {errors.START_DATE[0]}
                </p>
              )}
            </div>
            <div>
              <label
                data-testid="end-date-label"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {endLabel}
                {required && "*"}
              </label>
              <input
                data-testid="end-date-input"
                type="date"
                className={cn(
                  "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                  errors.END_DATE &&
                    errors.END_DATE.length > 0 &&
                    "!border-destructive",
                )}
                name="END_DATE_DISPLAY"
                value={endDate ? format(endDate, "yyyy-MM-dd") : ""}
                onChange={(e) => {
                  if (!e.target.value) {
                    handleDateChange(1)(undefined);
                    return;
                  }
                  // Parse the date in local timezone by appending the time component
                  const [year, month, day] = e.target.value
                    .split("-")
                    .map(Number);
                  const date = new Date(year, month - 1, day, 12); // Use noon to avoid any timezone issues
                  handleDateChange(1)(date);
                }}
              />
              <input
                type="hidden"
                name="END_DATE"
                value={endDate ? endDate.toISOString() : ""}
              />
              {errors.END_DATE && errors.END_DATE.length > 0 && (
                <p className="text-sm text-destructive mt-1">
                  {errors.END_DATE[0]}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn("grid gap-2", className)}
      data-testid="date-range-picker-EVENT_DATE_RANGE"
    >
      <input
        type="hidden"
        name="START_DATE"
        value={startDate ? startDate.toISOString() : ""}
      />
      <input
        type="hidden"
        name="END_DATE"
        value={endDate ? endDate.toISOString() : ""}
      />

      <ClientOnly serverRender={serverRenderPlaceholder}>
        {clientSidePopover}
      </ClientOnly>

      {/* In desktop view, use deduplicated errors or combine them if _deduplicated isn't provided */}
      {!isMobile && (
        <>
          {errors._deduplicated && errors._deduplicated.length > 0 ? (
            errors._deduplicated.map((error, i) => (
              <p key={i} className="text-sm text-destructive">
                {error}
              </p>
            ))
          ) : (
            <>
              {/* Display individual errors if _deduplicated is not provided */}
              {errors.START_DATE &&
                errors.START_DATE.map((error, i) => (
                  <p key={`start-${i}`} className="text-sm text-destructive">
                    {error}
                  </p>
                ))}
              {errors.END_DATE &&
                errors.END_DATE.map((error, i) => (
                  <p key={`end-${i}`} className="text-sm text-destructive">
                    {error}
                  </p>
                ))}
            </>
          )}
        </>
      )}
    </div>
  );
}
