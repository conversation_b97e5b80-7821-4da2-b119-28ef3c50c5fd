import * as cdk from 'aws-cdk-lib'
import * as ecs from 'aws-cdk-lib/aws-ecs'
import {FargateService, ICluster, OperatingSystemFamily, PidMode, Secret} from 'aws-cdk-lib/aws-ecs'
import * as route53 from 'aws-cdk-lib/aws-route53'
import * as targets from 'aws-cdk-lib/aws-route53-targets'
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager'
import {ISecret} from 'aws-cdk-lib/aws-secretsmanager'
import {Construct} from 'constructs'
import {ISecurityGroup, IVpc, SubnetSelection} from "aws-cdk-lib/aws-ec2";
import {IRepository} from "aws-cdk-lib/aws-ecr";
import {
    ApplicationListener,
    ApplicationLoadBalancer,
    ApplicationProtocol,
    ApplicationTargetGroup, ListenerAction,
    ListenerCertificate, ListenerCondition
} from "aws-cdk-lib/aws-elasticloadbalancingv2";
import * as iam from 'aws-cdk-lib/aws-iam';
import {ManagedPolicy} from 'aws-cdk-lib/aws-iam';
import * as codedeploy from 'aws-cdk-lib/aws-codedeploy';
import * as servicediscovery from 'aws-cdk-lib/aws-servicediscovery';
import {DiscoveryType, DnsRecordType} from 'aws-cdk-lib/aws-servicediscovery';
import {EcsDeployment} from "@cdklabs/cdk-ecs-codedeploy";

const DEFAULT_CONTAINER_HTTP_PORT = 8080

export interface EcrImageProps {
    getEcrRepo: (scope: Construct) => IRepository;
    imageTag: string;
}

export interface AppConstructProps {
    serviceName: string,
    appSpecificSecretsPrefix: string, // we use this to grant the task read access to all the secrets under this prefix
    serviceVersion: string,
    gitSha: string,
    imageProps: EcrImageProps,
    // Cross-account ECR configuration
    ecrCrossAccountAccess?: {
        enabled: boolean,
        sourceAccountId: string,
        region: string
    },
    domainName: string,
    publicLoadBalancerSubdomainNames: string[],
    redirectSubdomainToPaths?: { subdomain: string, path: string }[]
    vpc: IVpc,
    additionalEnvVars?: { [key: string]: string },
    additionalSecrets?: { [key: string]: ISecret },
    gitRepoUrl: string,
    scalingOptions: ScalingOptions,
    additionalPorts?: number[],
    deploymentEnvName: string,
    additionalSecurityGroups?: ISecurityGroup[],
    enableDatadogRemoteConfig?: boolean,
    healthcheckPath: string,
    healthCheckCommand: string,
}

interface ScalingOptions {
    resourceRequirements: ResourceRequirements,
    scaling: {
        minCapacity: number;  // Minimum number of tasks
        maxCapacity: number;  // Maximum number of tasks
    },
    spot?: boolean;  // Whether to use Fargate Spot for cost savings
}

interface ResourceRequirements {
    cpu: number,
    memory: number
}

function validateSecretNameWorksOkayForEcs(secret: ISecret) {
    if (secret.secretName.length < 7) {
        console.log(`warning: secret name is very short! this is probably not what you want! name=${secret.secretName}`)
        return;
    }
    const last7Chars = secret.secretName.substring(secret.secretName.length - 7);
    if (last7Chars.match(/-[a-zA-Z0-9]{6}/)) {
        throw new Error(`ERROR: secret name MUST NOT END with dash followed by 6 alphanumeric characters!\nECS will not be able to find the secret. It can't resolve the full ARN from the partial ARN when looking up the secret by name!\n For more details see: https://docs.aws.amazon.com/secretsmanager/latest/userguide/troubleshoot.html#ARN_secretnamehyphen\n OFFENDING SECRET NAME: ${secret.secretName}`)
    }
}

export class PublicLoadBalancedAppConstruct extends Construct {
    public readonly service: FargateService
    private readonly appContainer: ecs.ContainerDefinition
    private readonly _fargatePrivateSubnetGroupName = "fargate-private";

    private readonly datadogContainerMemoryReservation = 256;
    private readonly fireLensContainerMemoryReservation = 50;
    private readonly memoryBuffer = 50;
    private remainingCpuToAllocate: number;

    constructor(scope: Construct, id: string, props: AppConstructProps) {
        super(scope, id);

        this.remainingCpuToAllocate = props.scalingOptions.resourceRequirements.cpu;
        // Import shared cluster
        const importedClusterName = cdk.Fn.importValue('SivCentralEcsClusterName')
        const clusterArn = cdk.Fn.importValue('SivCentralEcsClusterArn')
        const extractedClusterName = cdk.Arn.extractResourceName(clusterArn, 'cluster')

        const cluster = ecs.Cluster.fromClusterAttributes(this, 'SharedCluster', {
            clusterName: extractedClusterName,
            vpc: props.vpc,
            securityGroups: [] // Add empty security groups array to avoid undefined
        })

        const datadogApiKeySecret = ecs.Secret.fromSecretsManager(secretsmanager.Secret.fromSecretNameV2(this,
            "DataDogApiKeySecret",
            "data-dog-api-key"
        ));

        // Calculate container memory
        const appContainerMemory = props.scalingOptions.resourceRequirements.memory
            - this.datadogContainerMemoryReservation
            - this.fireLensContainerMemoryReservation
            - this.memoryBuffer;

        const firelensContainerCpu = this.allocateCpuShares(102)
        const datadogContainerCpu = this.allocateCpuShares(102)
        const appContainerCpu = this.allocateCpuShares(this.remainingCpuToAllocate);


        const taskExecutionRole = new iam.Role(this, "TaskExecutionRole", {
            assumedBy: new iam.ServicePrincipal("ecs-tasks.amazonaws.com"),
            managedPolicies: [
                ManagedPolicy.fromAwsManagedPolicyName(
                    "service-role/AmazonECSTaskExecutionRolePolicy"
                ),
            ],
        })

        // Add cross-account ECR pull permissions if configured
        if (props.ecrCrossAccountAccess?.enabled) {
            // Grant permission to pull from source account ECR repositories
            const sourceAccountId = props.ecrCrossAccountAccess.sourceAccountId;
            const region = props.ecrCrossAccountAccess.region;

            // Add permissions to pull from the source account ECR repository
            // For cross-account ECR access, we need to use a more permissive approach
            // Based on the AWS documentation and best practices

            // First, add the GetAuthorizationToken permission which requires a * resource
            taskExecutionRole.addToPrincipalPolicy(new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'ecr:GetAuthorizationToken'
                ],
                resources: ['*'] // GetAuthorizationToken requires a * resource
            }));

            // Then, add resource-specific permissions for the ECR repository
            taskExecutionRole.addToPrincipalPolicy(new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'ecr:BatchCheckLayerAvailability',
                    'ecr:GetDownloadUrlForLayer',
                    'ecr:BatchGetImage',
                    'ecr:DescribeImages',
                    'ecr:DescribeRepositories'
                ],
                resources: [
                    // Allow access to specific ECR repository in the source account
                    `arn:aws:ecr:${region}:${sourceAccountId}:repository/*`
                ]
            }));
        }

        const taskDefinition = new ecs.FargateTaskDefinition(this, "AppTaskDef", {
            cpu: props.scalingOptions.resourceRequirements.cpu,
            memoryLimitMiB: props.scalingOptions.resourceRequirements.memory,
            family: props.serviceName,
            pidMode: PidMode.TASK,
            runtimePlatform: {
                operatingSystemFamily: OperatingSystemFamily.LINUX,
            },
            executionRole: taskExecutionRole,
        });


        // Add permissions to task role for Secrets Manager
        Object.keys(props.additionalSecrets ?? []).forEach((key) => {
                const secret: ISecret | undefined = props?.additionalSecrets?.[key]
                if (secret) {
                    validateSecretNameWorksOkayForEcs(secret)
                    secret.grantRead(
                        taskDefinition.taskRole
                    )
                    secret.grantRead(taskExecutionRole)
                }
            },
        );

        // Add containers in correct order
        this.appContainer = this.addAppContainerToTaskdef({
            taskDefinition,
            serviceName: props.serviceName,
            appContainerMemory: appContainerMemory,
            cpu: appContainerCpu,
            image: ecs.ContainerImage.fromEcrRepository(
                props.imageProps.getEcrRepo(this),
                props.imageProps.imageTag
            ),
            additionalContainerPorts: props.additionalPorts || [],
            deploymentEnv: props.deploymentEnvName,
            serviceVersion: props.serviceVersion,
            datadogApiKeySecret,
            gitSha: props.gitSha,
            additionalEnvVars: props.additionalEnvVars || {},
            gitRepoUrl: props.gitRepoUrl,
            additionalSecrets: props.additionalSecrets || {},
            enableDatadogRemoteConfig: props.enableDatadogRemoteConfig,
            healthCheckCommand: props.healthCheckCommand,
        });
        this.addDatadogAgentContainer(taskDefinition, datadogContainerCpu, datadogApiKeySecret, props.serviceName);
        this.addFireLensLogRouterToTaskDef(taskDefinition, firelensContainerCpu, props.serviceName);

        const securityGroups: ISecurityGroup[] = props.additionalSecurityGroups || []
        const appSubnetSelection: SubnetSelection = {subnetGroupName: this._fargatePrivateSubnetGroupName};

        const publicAppSubnets = props.vpc.selectSubnets({
            subnetGroupName: "public-app",
            onePerAz: true  // This ensures one subnet per AZ
        });


        const loadBalancer = new ApplicationLoadBalancer(this, 'AppLoadBalancer', {
            vpc: props.vpc,
            internetFacing: true,
            vpcSubnets: publicAppSubnets,
        });

        // Create blue and green target groups for blue-green deployment
        const blueTargetGroup = new ApplicationTargetGroup(this, "BlueTargetGroup", {
            vpc: props.vpc,
            port: 8080,
            protocol: ApplicationProtocol.HTTP,
            targetType: cdk.aws_elasticloadbalancingv2.TargetType.IP,
            healthCheck: {
                port: "8080",
                path: props.healthcheckPath,
                healthyThresholdCount: 3,
                unhealthyThresholdCount: 5,
                timeout: cdk.Duration.seconds(10),
                interval: cdk.Duration.seconds(30),
                healthyHttpCodes: "200-299",
                enabled: true
            },
            deregistrationDelay: cdk.Duration.seconds(30),
        });

        const greenTargetGroup = new ApplicationTargetGroup(this, "GreenTargetGroup", {
            vpc: props.vpc,
            port: 8080,
            protocol: ApplicationProtocol.HTTP,
            targetType: cdk.aws_elasticloadbalancingv2.TargetType.IP,
            healthCheck: {
                port: "8080",
                path: props.healthcheckPath,
                healthyThresholdCount: 3,
                unhealthyThresholdCount: 5,
                timeout: cdk.Duration.seconds(10),
                interval: cdk.Duration.seconds(30),
                healthyHttpCodes: "200-299",
                enabled: true
            },
            deregistrationDelay: cdk.Duration.seconds(30),
        });

        // Configure HTTPS listener for production traffic
        const productionListener = loadBalancer.addListener('HttpsListener', {
            port: 443,
            protocol: cdk.aws_elasticloadbalancingv2.ApplicationProtocol.HTTPS,
            certificates: [cdk.aws_elasticloadbalancingv2.ListenerCertificate.fromArn(
                cdk.Fn.importValue('SivConvertsWildcardCertificateArn')
            )],
            defaultAction: cdk.aws_elasticloadbalancingv2.ListenerAction.forward([blueTargetGroup])
        });

        props.redirectSubdomainToPaths?.map((rec, index) => {
            const hostHeader = `${rec.subdomain}.${props.domainName}`;
            return (
                productionListener.addAction(`${hostHeader}-RedirectTo-${rec.path.replace("/", "")}`, {
                    priority: 200 + index, // Use 200+ range to avoid conflicts with existing rules
                    conditions: [
                        ListenerCondition.hostHeaders([hostHeader]),
                        ListenerCondition.pathPatterns(['/']) // Only redirect root path
                    ],
                    action: ListenerAction.redirect({
                        host: '#{host}',
                        path: rec.path,
                        port: '#{port}',
                        protocol: '#{protocol}',
                        query: '#{query}',
                        permanent: true
                    })
                })
            );
        })

        // Add cross-subdomain redirects
        // This ensures users are always on the correct subdomain for the resource they're accessing
        props.redirectSubdomainToPaths?.forEach((rec, index) => {
            // For each subdomain, redirect paths that belong to other subdomains
            props.redirectSubdomainToPaths?.forEach((targetRec, targetIndex) => {
                if (rec.subdomain !== targetRec.subdomain) {
                    const sourceHostHeader = `${rec.subdomain}.${props.domainName}`;
                    const targetHostHeader = `${targetRec.subdomain}.${props.domainName}`;
                    
                    productionListener.addAction(`${rec.subdomain}-to-${targetRec.subdomain}-path-redirect`, {
                        priority: 300 + (index * 10) + targetIndex, // Use 300+ range for cross-subdomain redirects
                        conditions: [
                            ListenerCondition.hostHeaders([sourceHostHeader]),
                            ListenerCondition.pathPatterns([targetRec.path, `${targetRec.path}/*`])
                        ],
                        action: ListenerAction.redirect({
                            host: targetHostHeader,
                            path: '/#{path}',
                            port: '#{port}',
                            protocol: '#{protocol}',
                            query: '#{query}',
                            permanent: true
                        })
                    });
                }
            });
        });

        // Create test listener for testing new deployments
        let testListener: ApplicationListener | undefined;
        testListener = loadBalancer.addListener('TestListener', {
            port: 8443, // Different port for test traffic
            protocol: ApplicationProtocol.HTTPS,
            certificates: [ListenerCertificate.fromArn(
                cdk.Fn.importValue('SivConvertsWildcardCertificateArn')
            )],
            defaultAction: cdk.aws_elasticloadbalancingv2.ListenerAction.forward([greenTargetGroup])
        });

        // Create the service with CodeDeploy deployment controller
        this.service = new ecs.FargateService(this, "FargateService", {
            cluster,
            taskDefinition,
            vpcSubnets: appSubnetSelection,
            minHealthyPercent: 100,
            maxHealthyPercent: 200,
            healthCheckGracePeriod: cdk.Duration.seconds(30),
            securityGroups: securityGroups,
            desiredCount: props.scalingOptions.scaling.minCapacity,
            assignPublicIp: false,
            capacityProviderStrategies: props.scalingOptions.spot ? [
                {
                    capacityProvider: 'FARGATE_SPOT',
                    weight: 1
                }
            ] : undefined,
            deploymentController: {
                type: ecs.DeploymentControllerType.CODE_DEPLOY
            },
            circuitBreaker: undefined, // Not compatible with CODE_DEPLOY controller
        });

        // Associate the service with the blue target group initially
        this.service.attachToApplicationTargetGroup(blueTargetGroup);

        // Create the application
        const application = new codedeploy.EcsApplication(this, 'CodeDeployApplication', {
            applicationName: `${props.serviceName}-${props.deploymentEnvName}`,
        });

        // Create a new deployment group with a unique name to avoid conflicts
        const deploymentGroup = new codedeploy.EcsDeploymentGroup(this, 'EcsBlueGreenDeploymentGroup', {
            application,
            // Add 'v2' suffix to make the name unique
            deploymentGroupName: `${props.serviceName}-${props.deploymentEnvName}-dg-v2`,
            service: this.service,
            // Use the recommended blue/green deployment config
            blueGreenDeploymentConfig: {
                blueTargetGroup,
                greenTargetGroup,
                listener: productionListener,
                testListener: testListener,
                // keep blue instances around for 10 minutes in case we need to quickly roll back
                terminationWaitTime: cdk.Duration.minutes(2),
            },
            // Use ALL_AT_ONCE deployment configuration
            deploymentConfig: codedeploy.EcsDeploymentConfig.ALL_AT_ONCE,
            autoRollback: {
                failedDeployment: true,
                stoppedDeployment: true,
                deploymentInAlarm: false, //If we want to use this, would need to associate at least one CloudWatch alarm with the Deployment Group.
            }
        });


        new EcsDeployment({
            deploymentGroup,
            targetService: {
                taskDefinition,
                containerName: this.appContainerName({appName: props.serviceName}),
                containerPort: DEFAULT_CONTAINER_HTTP_PORT,
            },
        });

        // Add DNS record
        const hostedZone = route53.HostedZone.fromHostedZoneAttributes(this, 'HostedZone', {
            hostedZoneId: cdk.Fn.importValue('SivConvertsHostedZoneId'),
            zoneName: props.domainName
        });

        props.publicLoadBalancerSubdomainNames.map((subdomainName: string) => {
            new route53.ARecord(this, `ServiceDnsRecord-${subdomainName}`, {
                zone: hostedZone,
                recordName: `${subdomainName}.${props.domainName}`,
                target: route53.RecordTarget.fromAlias(
                    new targets.LoadBalancerTarget(loadBalancer)
                )
            })
        });


        const autoScalingGroup = this.service.autoScaleTaskCount({
            minCapacity: props.scalingOptions.scaling.minCapacity,
            maxCapacity: props.scalingOptions.scaling.maxCapacity
        });

        // CPU-based target tracking with fixed 50% target for Java services
        autoScalingGroup.scaleOnCpuUtilization('CpuScaling', {
            targetUtilizationPercent: 50,  // Lower target for Java apps
            scaleInCooldown: cdk.Duration.seconds(300),
            scaleOutCooldown: cdk.Duration.seconds(60)
        });
    }

    private addFireLensLogRouterToTaskDef(taskDefinition: ecs.TaskDefinition, firelensContainerCpu: number, appName: string): ecs.FirelensLogRouter {
        return taskDefinition.addFirelensLogRouter("FirelensLogRouter", {
            image: ecs.obtainDefaultFluentBitECRImage(taskDefinition, taskDefinition.defaultContainer?.logDriverConfig, "2.14.0"),
            dockerLabels: {
                "com.datadoghq.ad.instances": `[]`,
                "com.datadoghq.ad.check_names": '[]',
                "com.datadoghq.ad.init_configs": '[]',
            },
            essential: true,
            memoryReservationMiB: this.fireLensContainerMemoryReservation,
            firelensConfig: {
                type: ecs.FirelensLogRouterType.FLUENTBIT,
                options: {
                    enableECSLogMetadata: true,
                    configFileType: ecs.FirelensConfigFileType.FILE,
                    configFileValue: "/fluent-bit/configs/parse-json.conf",
                }
            },
            cpu: firelensContainerCpu,
            logging: new ecs.AwsLogDriver({
                streamPrefix: `${appName}-firelens`
            })
        });
    }

    private addAppContainerToTaskdef(params: {
        taskDefinition: ecs.TaskDefinition,
        serviceName: string,
        appContainerMemory: number,
        image: ecs.ContainerImage,
        additionalContainerPorts: number[],
        deploymentEnv: string,
        serviceVersion: string,
        datadogApiKeySecret: ecs.Secret,
        gitSha: string,
        gitRepoUrl: string,
        cpu: number,
        enableDatadogRemoteConfig?: boolean
        additionalEnvVars: { [key: string]: string },
        additionalSecrets: { [key: string]: ISecret },
        healthCheckCommand: string,
    }): ecs.ContainerDefinition {
        const containerPorts = [DEFAULT_CONTAINER_HTTP_PORT].concat((params.additionalContainerPorts || []))

        const ecsSecrets: { [key: string]: ecs.Secret } = Object.keys(params.additionalSecrets).reduce(
            (acc, key) => {
                let additionalSecret = params.additionalSecrets[key];
                return ({
                    ...acc,
                    [key]: ecs.Secret.fromSecretsManager(additionalSecret)
                });
            },
            {}
        )

        return params.taskDefinition.addContainer("AppImage", {
            containerName: this.appContainerName({appName: params.serviceName}),
            cpu: params.cpu,
            memoryLimitMiB: params.appContainerMemory,
            image: params.image,
            startTimeout: cdk.Duration.minutes(5),
            stopTimeout: cdk.Duration.seconds(90),
            portMappings: containerPorts.map((port) => {
                return {containerPort: port}
            }),
            dockerLabels: {
                "com.datadoghq.ad.instances": `[{"host": "%%host%%", "port": "${DEFAULT_CONTAINER_HTTP_PORT}"}]`,
                "com.datadoghq.ad.check_names": '[]',
                "com.datadoghq.ad.init_configs": '[{}]',
                env: params.deploymentEnv,
                version: params.serviceVersion,
                service: params.serviceName,
            },
            environment: {
                ...params.additionalEnvVars,
                DD_LOGS_INJECTION: "true",
                DD_TRACE_SAMPLE_RATE: "1", //Do not sample traces "tracing without limits" feature of datadog
                DD_ENV: params.deploymentEnv,
                DD_SERVICE: params.serviceName,
                DD_VERSION: params.serviceVersion,
                DD_PROFILING_ENABLED: "true",
                DD_INTEGRATION_OPENTELEMETRY_BETA_ENABLED: "true",
                DD_GIT_COMMIT_SHA: params.gitSha,
                DD_GIT_REPOSITORY_URL: params.gitRepoUrl,
                DD_DYNAMIC_INSTRUMENTATION_ENABLED: "true",
                DD_CODE_ORIGIN_FOR_SPANS_ENABLED: "true"
            },
            secrets: {
                ...ecsSecrets,
            },
            essential: true,
            logging: this.datadogFirelensLogDriver(params.datadogApiKeySecret, "node", params.serviceName),
        });
    }

    private appContainerName(params: {
        appName: string;
    }) {
        return `${params.appName}-app`;
    }

    private allocateCpuShares(sharesToAllocate: number) {
        if (this.remainingCpuToAllocate - sharesToAllocate < 0) {
            throw `ERROR: not enough cpu to allocate! Remaining shares to allocate: ${this.remainingCpuToAllocate} tried to allocate ${sharesToAllocate}; there is a deficit of ${this.remainingCpuToAllocate - sharesToAllocate}`;
        }
        this.remainingCpuToAllocate -= sharesToAllocate;
        return sharesToAllocate;
    }

    private addDatadogAgentContainer(taskDefinition: ecs.TaskDefinition, datadogContainerCpu: number, datadogApiKeySecret: ecs.Secret, appName: string, deploymentEnv: string = 'prod'): ecs.ContainerDefinition {
        return taskDefinition.addContainer("DatadogAgentContainer", {
            image: ecs.ContainerImage.fromRegistry("public.ecr.aws/datadog/agent:latest"),
            memoryReservationMiB: this.datadogContainerMemoryReservation,
            logging: this.datadogFirelensLogDriver(datadogApiKeySecret, "dd-agent", appName),
            cpu: datadogContainerCpu,
            dockerLabels: {},
            environment: {
                ECS_FARGATE: "true",
                DD_ENV: deploymentEnv,
                DD_SITE: "datadoghq.com",
                DD_DOGSTATSD_NON_LOCAL_TRAFFIC: "true",
                DD_DOGSTATSD_TAGS: `env:${deploymentEnv} service:${appName}`,
                DD_APM_ENABLED: "true",
                DD_APM_NON_LOCAL_TRAFFIC: "true",
                DD_DOCKER_LABELS_AS_TAGS: '{"env":"env", "service":"service"}',
                DD_DOGSTATSD_TAG_CARDINALITY: "orchestrator",
                DD_LOGS_INJECTION: "true",
                DD_LOG_LEVEL: "WARN"
            },
            secrets: {
                DD_API_KEY: datadogApiKeySecret
            },
            essential: true,
            portMappings: [
                {
                    containerPort: 8126,
                    protocol: ecs.Protocol.TCP
                },
                {
                    containerPort: 8125,
                    protocol: ecs.Protocol.UDP
                },
                {
                    containerPort: 5001, //this is the IPC channel for datadog; see https://docs.datadoghq.com/agent/configuration/network/
                    protocol: ecs.Protocol.TCP,
                }
            ],
        });
    }

    private datadogFirelensLogDriver(datadogApiKeySecret: ecs.Secret, ddSource: string, ddService: string): ecs.FireLensLogDriver {
        const options: { [key: string]: string; } = {
            Name: "datadog",
            compress: "gzip",
            Host: "http-intake.logs.datadoghq.com",
            TLS: "on",
            dd_source: ddSource,
            dd_message_key: "message",
            provider: "ecs",
            dd_service: ddService,
        };

        const secretOptions: { [key: string]: Secret } = {
            "apikey": datadogApiKeySecret
        };
        return new ecs.FireLensLogDriver({
            options: options,
            secretOptions: secretOptions
        });
    }
}
