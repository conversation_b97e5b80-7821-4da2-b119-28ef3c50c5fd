import * as cdk from 'aws-cdk-lib';
import {CfnOutput, Fn} from 'aws-cdk-lib';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import {ISecret, Secret} from 'aws-cdk-lib/aws-secretsmanager';
import * as elasticache from 'aws-cdk-lib/aws-elasticache';
import * as s3 from 'aws-cdk-lib/aws-s3';
import {Bucket, IBucket} from 'aws-cdk-lib/aws-s3';
import {Construct} from 'constructs';
import {AppConstructProps, EcrImageProps, PublicLoadBalancedAppConstruct} from "./PublicLoadBalancedAppConstruct";
import {IVpc, Port, SecurityGroup, Vpc} from "aws-cdk-lib/aws-ec2";
import {
    AuroraPostgresEngineVersion,
    ClusterInstance,
    Credentials,
    DatabaseCluster,
    DatabaseClusterEngine,
    PerformanceInsightRetention
} from "aws-cdk-lib/aws-rds";
import {DeploymentEnv} from "../types";
import * as route53 from "aws-cdk-lib/aws-route53";
import {CfnServerlessCache} from "aws-cdk-lib/aws-elasticache";
import {IHostedZone} from "aws-cdk-lib/aws-route53";

const DATABASE_SUBNET_GROUP_NAME = "private-isolated";

type ResourceConfig = {
    database: {
        serverlessV2MinCapacity: number;
        serverlessV2MaxCapacity: number;
    };
    fargate: {
        cpu: number;
        memory: number;
        minCapacity: number;
        maxCapacity: number;
        useSpot: boolean;
    };
    elasticache: {
        dataStorage: number;
        ecpuPerSecond: number;
    };
};

type AppProps = {
    sivAdminPortalClerkPublicKey: string;
    userFacingSivClerkPublicKey: string;
    javaImageProps: EcrImageProps;
    nodeImageProps: EcrImageProps;
    gitSha: string;
    gitRepoUrl: string;
    version: string;
    deploymentEnvName: DeploymentEnv;
    nodeEnv: "production" | "staging";
    domainName: string;
    additionalSecretNames?: { [key: string]: string }, //should be used only to pass secrets that apply to one env and not another; eg enabling staging-specific env vars
    additionalEnvVars?: { [key: string]: string }, //should be used only to pass env vars that apply to one env and not another; eg enabling staging-specific env vars
    // Google OAuth client ID for authentication
    googleOauthClientId: string;
    // Whether to append environment suffix to resource names, defaults to false
    suffixResourceNames?: boolean;
    // Resource configuration for the environment
    resourceConfig?: ResourceConfig;
    // Cross-account ECR configuration
    ecrCrossAccountAccess?: {
        enabled: boolean;
        sourceAccountId: string;
        region: string;
    };
    fileUploadBucket: s3.IBucket;
    adminPortalClerkEmailCnameRecords: ClerkCnameRecord[],
    userFacingAppClerkEmailCnameRecords: ClerkCnameRecord[]
};

type DatabaseConfig = {
    DATABASE_NAME: string;
    dbUsername: string;
    databasePasswordSecret: Secret;
    databaseCluster: DatabaseCluster
};

type ClerkCnameRecord = { recordName: string, domainName: string };

export class MonolithAppStack extends cdk.Stack {

    private readonly _adminSubdomain = "admin";

    private readonly _userFacingAppSubdomain = "appv2";

    constructor(scope: Construct, id: string, appProps: AppProps, props?: cdk.StackProps) {
        super(scope, id, props);

        const vpc = Vpc.fromLookup(this, 'SivVpc', {
            vpcName: 'FoundationNetworkingStack/siv-vpc',
            region: 'us-west-2'
        });
        const databaseConfig = this.configureDatabase(vpc);

        //TODO bring this back as a dependency when we straighten out this circular dependency issue
        const fileUploadBucket = appProps.fileUploadBucket;

        // Get the Google OAuth client ID from appProps
        //TODO: get rid of this env var entirely once we confirm clerk is working!
        const googleOauthClientId = appProps.googleOauthClientId;
        const googleOauthClientSecret = secretsmanager.Secret.fromSecretNameV2(this,
            "SivGoogleOauthClientSecret",
            "apps/monolith/siv-google-oauth-clientsecret"
        );

        const sivAdminPortalClerkSecretKey = secretsmanager.Secret.fromSecretNameV2(this,
            "SivAdminPortalClerkSecretKey",
            "apps/monolith/siv-admin-portal-clerk-secret-key"
        );

        const userFacingSivClerkSecretKey = secretsmanager.Secret.fromSecretNameV2(this,
            "UserFacingSivClerkSecretKey",
            "apps/monolith/siv-user-facing-app-clerk-secret-key"
        );

        const securityGroup = new SecurityGroup(this, "ElasticacheSecurityGroup", {
            vpc: vpc,
            description: "Allow monolith app fargate service to access to elasticache",
            allowAllOutbound: true,
        })

        const isolatedDbSubnets = vpc.selectSubnets({subnetGroupName: DATABASE_SUBNET_GROUP_NAME})

        // Get resource config or use defaults for production
        const resourceConfig = appProps.resourceConfig || {
            database: {
                serverlessV2MinCapacity: 0.5,
                serverlessV2MaxCapacity: 3
            },
            fargate: {
                cpu: 1024,
                memory: 2048,
                minCapacity: 2,
                maxCapacity: 4,
                useSpot: false
            },
            elasticache: {
                dataStorage: 1,
                ecpuPerSecond: 10000
            }
        };

        // Create elasticache with environment-specific name
        const valkey = new elasticache.CfnServerlessCache(this, "Elasticache", {
            engine: "valkey",
            majorEngineVersion: "8",
            serverlessCacheName: 'siv-web-valkey',
            securityGroupIds: [securityGroup.securityGroupId],
            subnetIds: isolatedDbSubnets.subnetIds,
            cacheUsageLimits: {
                dataStorage: {maximum: resourceConfig.elasticache.dataStorage, unit: "GB"},
                ecpuPerSecond: {maximum: resourceConfig.elasticache.ecpuPerSecond}
            }
        })

        const honoWebAppConstruct = this.honoWebAppConstruct({
            ...appProps,
            valkey,
            vpc
        }, {
            integrationsSubdomain: "siv-integrations",
            adminSubdomain: this._adminSubdomain,
            userFacingAppSubdomain: this._userFacingAppSubdomain,
            databaseConfig,
            googleOauthClientSecret,
            sivAdminPortalClerkSecretKey,
            userFacingSivClerkSecretKey,
            googleOauthClientId,
            fileUploadBucket,
            sivAdminPortalClerkPublicKey: appProps.sivAdminPortalClerkPublicKey,
            userFacingSivClerkPublicKey: appProps.userFacingSivClerkPublicKey
        });

        // Allow the service to connect to elasticache
        securityGroup.connections.allowFrom(honoWebAppConstruct.service, Port.tcpRange(6379, 6380), 'Allow Fargate service to connect to elasticache');

        // Allow the service to connect to the database
        databaseConfig.databaseCluster.connections.allowFrom(
            honoWebAppConstruct.service, // This targets the Fargate service's security group
            Port.tcp(5432),
            'Allow Fargate service to connect to Aurora'
        );

        const bastionHostSecurityGroupId = Fn.importValue('FoundationNetworkingStack-BastionHostSecurityGroupId');
        const bastionHostSecurityGroup = SecurityGroup.fromSecurityGroupId(this, "BastionHostSecurityGroup", bastionHostSecurityGroupId.valueOf());
        databaseConfig.databaseCluster.connections.allowFrom(
            bastionHostSecurityGroup,
            Port.tcp(5432),
            'Allow Bastion host to connect to Aurora so we can run migrations in our CD pipeline'
        );

        // Grant the Fargate service permissions to the S3 bucket
        fileUploadBucket.grantReadWrite(honoWebAppConstruct.service.taskDefinition.taskRole);


        this.configureClerkDnsRecords({
            userFacingAppClerkEmailCnameRecords: appProps.userFacingAppClerkEmailCnameRecords,
            adminPortalClerkEmailCnameRecords: appProps.adminPortalClerkEmailCnameRecords,
            domainName: appProps.domainName
        });
    }

    private configureClerkDnsRecords(props: {
        domainName: string,
        adminPortalClerkEmailCnameRecords: ClerkCnameRecord[],
        userFacingAppClerkEmailCnameRecords: ClerkCnameRecord[]
    }) {
        const hostedZone = route53.HostedZone.fromHostedZoneAttributes(this, `SivHostedZone-clerk-import`, {
            hostedZoneId: cdk.Fn.importValue('SivConvertsHostedZoneId'),
            zoneName: props.domainName
        });
        new route53.CnameRecord(this, `ClerkFrontendApiCname-admin`, {
            zone: hostedZone,
            recordName: "clerk.admin",
            domainName: "frontend-api.clerk.services"
        })

        new route53.CnameRecord(this, `UserFacingAppClerkFrontendApiCname-root`, {
            zone: hostedZone,
            recordName: "clerk",
            domainName: "frontend-api.clerk.services"
        })

        new route53.CnameRecord(this, `ClerkAccountPortalCname-admin`, {
            zone: hostedZone,
            recordName: "accounts.admin",
            domainName: "accounts.clerk.services"
        })
        new route53.CnameRecord(this, `UserFacingAppClerkAccountPortalCname-root`, {
            zone: hostedZone,
            recordName: "accounts",
            domainName: "accounts.clerk.services"
        })

        this.configureClerkEmailCnameRecords({
            idSuffix: "admin",
            hostedZone,
            clerkCnameRecords: props.adminPortalClerkEmailCnameRecords
        })
        this.configureClerkEmailCnameRecords({
            idSuffix: "root",
            hostedZone,
            clerkCnameRecords: props.userFacingAppClerkEmailCnameRecords
        })
    }

    private honoWebAppConstruct(appProps: AppProps & { valkey: CfnServerlessCache; vpc: IVpc }, tempMigrationProps: {
        sivAdminPortalClerkPublicKey: string;
        userFacingSivClerkPublicKey: string;
        userFacingAppSubdomain: string;
        integrationsSubdomain: string;
        adminSubdomain: string;
        databaseConfig: DatabaseConfig;
        googleOauthClientSecret: ISecret;
        sivAdminPortalClerkSecretKey: ISecret;
        userFacingSivClerkSecretKey: ISecret;
        googleOauthClientId: string;
        fileUploadBucket: IBucket
    }) {
        // Get resource config or use defaults for production
        const resourceConfig = appProps.resourceConfig || {
            fargate: {
                cpu: 1024,
                memory: 2048,
                minCapacity: 2,
                maxCapacity: 4,
                useSpot: false
            }
        };

        // Environment-specific secrets with appropriate suffixes
        const inngestEventKeySecret = secretsmanager.Secret.fromSecretNameV2(this, "InngestEventKeySecret", `apps/monolith/inngest-event-key`);
        const inngestSigningKeySecret = secretsmanager.Secret.fromSecretNameV2(this, "InngestSigningKeySecret", `global/inngest/signing-key`);
        const sivWebDatadogAppKeySecret = secretsmanager.Secret.fromSecretNameV2(this, "DatadogAppKeySecret", `apps/monolith/siv-web-datadog-app-key`);

        // Create session secret if it doesn't exist
        const sessionSecret = new secretsmanager.Secret(this, 'SessionSecret', {
            secretName: `apps/monolith/session-secret`,
            description: `Session secret for web application in ${appProps.deploymentEnvName} environment`,
            generateSecretString: {
                passwordLength: 64,
                excludeCharacters: "\"@/\\"
            }
        });

        // Use the googleOauthClientId passed from the constructor
        const googleOauthClientId = tempMigrationProps.googleOauthClientId;

        // Use domain name from props
        const domainName = appProps.domainName;
        const formsSubdomain = "forms";

        const additionalEnvSpecificSecrets = appProps.additionalSecretNames ? Object.entries(appProps.additionalSecretNames).reduce((acc, [key, secretName]) => {
            acc[key] = secretsmanager.Secret.fromSecretNameV2(this, `secret-${key}`, secretName);
            return acc;
        }, {} as { [key: string]: ISecret }) : {};


        const internalApiSecret = new Secret(this, "SivwebInternalApiSecret", {
            secretName: "apps/monolith/siv-web-internal-apisecret",
            description: "shared secret used for other internal apps to access the internal siv-web monolith's api",
        })

        let appConstructProps: AppConstructProps = {
            redirectSubdomainToPaths: [
                {subdomain: this._userFacingAppSubdomain, path: "/app"}, //redirect the appv2 domain name to /app
                {subdomain: this._adminSubdomain, path: "/admin"} //redirect the admin subdomain to /admin path
            ],
            appSpecificSecretsPrefix: `apps/monolith`,
            serviceName: `siv-web`,
            deploymentEnvName: appProps.deploymentEnvName,
            serviceVersion: appProps.version,
            additionalPorts: [8081],
            // Add internal API endpoint that creates a new subdomain in the siv-services.net private hosted zone
            gitSha: appProps.gitSha,
            gitRepoUrl: appProps.gitRepoUrl,
            imageProps: appProps.nodeImageProps,
            enableDatadogRemoteConfig: true,
            domainName: domainName,
            publicLoadBalancerSubdomainNames: [
                tempMigrationProps.integrationsSubdomain,
                tempMigrationProps.adminSubdomain,
                tempMigrationProps.userFacingAppSubdomain,
                formsSubdomain,
                'api',
                'siv-web'
            ],

            vpc: appProps.vpc,
            healthcheckPath: "/health",
            healthCheckCommand: "pnpm healthcheck",
            // Pass cross-account ECR configuration if provided
            ecrCrossAccountAccess: appProps.ecrCrossAccountAccess,
            scalingOptions: {
                resourceRequirements: {
                    memory: resourceConfig.fargate.memory,
                    cpu: resourceConfig.fargate.cpu
                },
                scaling: {
                    minCapacity: resourceConfig.fargate.minCapacity,
                    maxCapacity: resourceConfig.fargate.maxCapacity
                },
                spot: resourceConfig.fargate.useSpot
            },
            additionalEnvVars: {
                "LEAD_ASSIGNMENT_INBOUND_WEBHOOK_URL": `https://${tempMigrationProps.integrationsSubdomain}.${domainName}/api/integrations/highlevel/webhook`,
                "REPORT_INNGEST_FUNCTION_FAILURES_TO_DATADOG": "true",
                "DATABASE_USER": tempMigrationProps.databaseConfig.dbUsername,
                "DATABASE_HOST": tempMigrationProps.databaseConfig.databaseCluster.clusterEndpoint.hostname,
                "DATABASE_PORT": `${tempMigrationProps.databaseConfig.databaseCluster.clusterEndpoint.port}`,
                "DATABASE_NAME": tempMigrationProps.databaseConfig.DATABASE_NAME,
                "OAUTH_GOOGLE_CLIENT_ID": googleOauthClientId,
                "LOGGER_JSON": "true",
                "FORM_CDN_URL": `https://sivform-cdn.${domainName}`,
                "FORM_EMBED_BASE_URL": `https://${formsSubdomain}.${domainName}`,
                "DEPLOYMENT_ENV": appProps.deploymentEnvName,
                "NODE_ENV": appProps.nodeEnv,
                "S3_LEAD_FORM_FILE_UPLOAD_BUCKET": tempMigrationProps.fileUploadBucket.bucketName,
                "INTERNAL_API_PORT": "8081",
                "CLERK_PUBLIC_KEY": tempMigrationProps.sivAdminPortalClerkPublicKey,
                "SIV_ADMIN_CLERK_PUBLISHABLE_KEY": tempMigrationProps.sivAdminPortalClerkPublicKey,
                "USER_FACING_SIV_CLERK_PUBLISHABLE_KEY": tempMigrationProps.userFacingSivClerkPublicKey,
                ...appProps.additionalEnvVars
            },
            additionalSecrets: {
                "OAUTH_GOOGLE_CLIENT_SECRET": tempMigrationProps.googleOauthClientSecret,
                "DATABASE_PASSWORD": tempMigrationProps.databaseConfig.databasePasswordSecret,
                "INNGEST_EVENT_KEY": inngestEventKeySecret,
                "INNGEST_SIGNING_KEY": inngestSigningKeySecret,
                "DD_APP_KEY": sivWebDatadogAppKeySecret,
                "SESSION_SECRET": sessionSecret,
                "INTERNAL_API_SECRET": internalApiSecret,
                "SIV_ADMIN_CLERK_SECRET_KEY": tempMigrationProps.sivAdminPortalClerkSecretKey,
                "USER_FACING_SIV_CLERK_SECRET_KEY": tempMigrationProps.userFacingSivClerkSecretKey,
                ...additionalEnvSpecificSecrets
            }
        };


        return new PublicLoadBalancedAppConstruct(this, "SivWebApp", appConstructProps);
    }

    private configureClerkEmailCnameRecords(props: {
        idSuffix: string,
        hostedZone: IHostedZone,
        clerkCnameRecords: ClerkCnameRecord[]
    }) {
        props.clerkCnameRecords.map((rec) => {
            new route53.CnameRecord(this, `ClerkEmailPortalCname-${rec.recordName}-${props.idSuffix}`, {
                zone: props.hostedZone,
                recordName: rec.recordName,
                domainName: rec.domainName
            })
        })

    }

    private configureDatabase(vpc: IVpc): DatabaseConfig {
        // Get resource config from stack props
        const resourceConfig = this.node.tryGetContext('resourceConfig') || {
            database: {
                serverlessV2MinCapacity: 0.5,
                serverlessV2MaxCapacity: 3
            }
        };

        // Get deployment environment name
        const deploymentEnvName = this.node.tryGetContext('deploymentEnvName') || 'prod';

        // Environment-specific secrets with appropriate suffixes
        const secretSuffix = deploymentEnvName === 'prod' ? '' : `-${deploymentEnvName}`;

        // Use environment-specific database names
        const DATABASE_NAME = deploymentEnvName === 'prod' ? 'sivmonolithdb' : `sivmonolithdb-${deploymentEnvName}`;
        const dbUsername = deploymentEnvName === 'prod' ? 'sivmonolithapp' : `sivmonolithapp-${deploymentEnvName}`;

        const databasePasswordSecret = new Secret(this, 'DBPasswordSecret', {
            secretName: `apps/monolith/pg-database-password${secretSuffix}`,
            description: `RDS database auto-generated user password for ${deploymentEnvName} environment`,
            generateSecretString: {
                passwordLength: 30,
                excludeCharacters: "\"@/\\",
            }
        });

        const databaseCluster = new DatabaseCluster(this, 'PgDatabase', {
            vpc: vpc,
            // storageEncrypted: true, //TODO: take a snapshot of the cluster, create a new cluster, load the data from snapshot, and swap the clusters
            vpcSubnets: {
                subnetGroupName: DATABASE_SUBNET_GROUP_NAME //dbs should not have internet access
            },
            enableDataApi: true,
            defaultDatabaseName: DATABASE_NAME,
            credentials: Credentials.fromPassword(dbUsername, databasePasswordSecret.secretValue),
            engine: DatabaseClusterEngine.auroraPostgres({version: AuroraPostgresEngineVersion.VER_16_6}),
            serverlessV2MinCapacity: resourceConfig.database.serverlessV2MinCapacity,
            serverlessV2MaxCapacity: resourceConfig.database.serverlessV2MaxCapacity,
            writer: ClusterInstance.serverlessV2("PgDatabaseWriter", {
                //since we're serving traffic across the US, this is a good time window; 3:00 AM PT is 6:00 AM ET
                preferredMaintenanceWindow: "sun:03:00-sun:03:30",
                autoMinorVersionUpgrade: true,
                enablePerformanceInsights: true,
                performanceInsightRetention: PerformanceInsightRetention.DEFAULT,
            }),
            deletionProtection: true
        })


        new CfnOutput(this, "DatabaseClusterHost", {
            exportName: "DatabaseClusterHost",
            value: databaseCluster.clusterEndpoint.hostname
        });

        new CfnOutput(this, "DatabaseClusterPort", {
            exportName: "DatabaseClusterPort",
            value: `${databaseCluster.clusterEndpoint.port}`
        });
        return {DATABASE_NAME, dbUsername, databasePasswordSecret, databaseCluster};
    }
}

