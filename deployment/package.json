{"name": "deployment", "version": "0.1.0", "bin": {"deployment": "bin/deployment.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "22.7.9", "aws-cdk": "2.175.1", "aws-cdk-lib": "2.175.1", "constructs": "^10.4.2", "datadog-cdk-constructs-v2": "^2.2.0", "esbuild": "^0.21.3", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@cdklabs/cdk-ecs-codedeploy": "^0.0.387"}}