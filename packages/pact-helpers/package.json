{"name": "@siv/pact-helpers", "version": "1.0.0", "description": "Shared PACT contract testing helpers for SIV applications", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "bin": {"publish-pacts": "./dist/bin/publish-pacts.js", "track-deployment": "./dist/bin/track-deployment.js"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "check-types": "tsc --noEmit", "lint": "eslint --ext .ts ./src"}, "dependencies": {"@pact-foundation/pact": "^15.0.1", "@pact-foundation/pact-node": "^10.18.0", "axios": "^1.6.0", "dotenv": "^16.3.2", "dotenv-flow": "^4.0.1", "minimist": "^1.2.8"}, "devDependencies": {"@types/dotenv-flow": "^3.3.3", "@types/minimist": "^1.2.5", "tsup": "^8.0.2", "typescript": "^5.0.0"}, "files": ["dist", "src"]}