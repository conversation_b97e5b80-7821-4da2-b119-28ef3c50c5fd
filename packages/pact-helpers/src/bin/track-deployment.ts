#!/usr/bin/env node

/**
 * Script to check if deployment is possible and record deployments in the Pact broker
 * Usage:
 *   - Check: track-deployment check --env <environment> --participant <participant-name> [--participant <participant-name-2> ...]
 *   - Record: track-deployment record --env <environment> --participant <participant-name> [--participant <participant-name-2> ...]
 *
 * Environment should be either 'staging' or 'production'
 * Specify one or more participants with the --participant flag
 *
 * Before running checks, ensure that:
 * 1. Pacts have been published to the broker
 * 2. Verification results have been published (using publish-verification-results script)
 *
 * Examples:
 *   track-deployment check --env staging --participant siv-web --participant siv-web-event-provider
 *   track-deployment record --env production --participant highlevel-file-sync-workflow
 */

// Display usage if no arguments provided
if (process.argv.length <= 2) {
  console.log(`
Pact Deployment Tracking Script
------------------------------
Usage: track-deployment ACTION --env ENVIRONMENT --participant PARTICIPANT_NAME [--participant PARTICIPANT_NAME2 ...]

Parameters:
  ACTION              Either 'check' or 'record'
                      - check: Check if a service can be safely deployed
                      - record: Record a deployment of a service
  --env ENV           The environment to check/record for ('staging' or 'production')
  --participant NAME  Specify one or more participant names (can be used multiple times)

Prerequisites:
  Before running checks, ensure that:
  1. Pacts have been published to the broker
  2. Verification results have been published (using publish-verification-results script)

Examples:
  track-deployment check --env staging --participant siv-web --participant siv-web-event-provider
  track-deployment record --env production --participant highlevel-file-sync-workflow
`);
  process.exit(0);
}

import { execSync } from 'child_process';
import path from 'path';
import * as dotenv from 'dotenv-flow';
import { fileURLToPath } from 'url';

// Parse command line arguments
let action: 'check' | 'record' | undefined;
let environment: 'staging' | 'production' | undefined;
let participants: string[] = [];

// Process arguments
for (let i = 2; i < process.argv.length; i++) {
  const arg = process.argv[i];

  if (arg === 'check' || arg === 'record') {
    action = arg;
  } else if (arg === '--env') {
    // Get the environment from the next argument
    const envValue = process.argv[++i];
    if (envValue === 'staging' || envValue === 'production') {
      environment = envValue;
    }
  } else if (arg === '--participant') {
    // Get the participant name from the next argument
    const participant = process.argv[++i];
    if (participant) {
      participants.push(participant);
    }
  }
}

// Validate the input
if (!action || (action !== 'check' && action !== 'record')) {
  console.error('Error: Please specify either "check" or "record" as an argument');
  console.error('Usage: track-deployment check|record --env staging|production --participant PARTICIPANT_NAME [--participant PARTICIPANT_NAME2 ...]');
  process.exit(1);
}

if (!environment) {
  console.error('Error: Please specify environment with --env staging|production');
  console.error('Usage: track-deployment check|record --env staging|production --participant PARTICIPANT_NAME [--participant PARTICIPANT_NAME2 ...]');
  process.exit(1);
}

if (participants.length === 0) {
  console.error('Error: Please specify at least one participant with --participant');
  console.error('Usage: track-deployment check|record --env staging|production --participant PARTICIPANT_NAME [--participant PARTICIPANT_NAME2 ...]');
  process.exit(1);
}

// Use process.cwd() which gives the current working directory
// This should be the project root when running this script
const projectRoot = process.cwd();

// Load environment variables using dotenv-flow
try {
  // This will load .env, .env.local, .env.test, .env.test.local as appropriate
  dotenv.config({
    node_env: 'test',  // Specify 'test' environment
    path: projectRoot, // Pass the directory, not the file
    default_node_env: 'test'
  });

  console.log('Environment variables loaded from:', projectRoot);
} catch (error) {
  console.error('Exception when loading environment variables:', error);
}

// Get broker details from environment variables
const brokerBaseUrl = process.env.PACT_BROKER_URL;
const brokerUsername = process.env.PACT_BROKER_USERNAME;
const brokerPassword = process.env.PACT_BROKER_PASSWORD;

// Check if required variables are set
if (!brokerBaseUrl || !brokerUsername || !brokerPassword) {
  console.error('Error: Pact broker details not found in environment variables!');
  console.error('Make sure PACT_BROKER_URL, PACT_BROKER_USERNAME, and PACT_BROKER_PASSWORD are set.');
  process.exit(1);
}

// Get the current git commit hash
const getGitCommit = (): string => {
  return execSync('git rev-parse HEAD').toString().trim();
};

// Get the current git branch
const getGitBranch = (): string => {
  return execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
};

const gitCommit = process.env.SEMAPHORE_GIT_SHA || getGitCommit();

if (action === 'check') {
  // Build a command that checks all participants at once
  const selectors = participants.map(p => `--pacticipant=${p} --version=${gitCommit}`).join(' ');

  console.log(`Checking if participants can be deployed to ${environment}...`);
  console.log(`Participants: ${participants.join(', ')} (version ${gitCommit})`);

  // Find the pact-broker binary path
  let pactBrokerPath = 'pact-broker';
  try {
    // Try to find the binary path relative to the current working directory
    if (process.platform === 'win32') {
      pactBrokerPath = './node_modules/.bin/pact-broker.cmd';
    } else {
      pactBrokerPath = './node_modules/.bin/pact-broker';
    }
  } catch (e) {
    console.warn('Could not determine pact-broker path, using default');
  }

  try {
    const command = [
      `${pactBrokerPath} can-i-deploy`,
      selectors,
      `--to=${environment}`,
      `--broker-base-url=${brokerBaseUrl}`,
      `--broker-username=${brokerUsername}`,
      `--broker-password=${brokerPassword}`
    ].join(' ');

    execSync(command, { stdio: 'inherit' });
    console.log(`✅ Deployment to ${environment} is safe!`);
  } catch (error) {
    console.error(`❌ Cannot safely deploy to ${environment}:`);
    console.error('Some pact verifications have failed. Check the output above for details.');

    // Exit with error - we want to prevent deployments that would break contracts
    process.exit(1);
  }
} else if (action === 'record') {
  // Record deployment for each participant
  for (const participantName of participants) {
    console.log(`Recording deployment of ${participantName} version ${gitCommit} to ${environment}...`);

    // Find the pact-broker binary path
    let pactBrokerPath = 'pact-broker';
    try {
      // Try to find the binary path relative to the current working directory
      if (process.platform === 'win32') {
        pactBrokerPath = './node_modules/.bin/pact-broker.cmd';
      } else {
        pactBrokerPath = './node_modules/.bin/pact-broker';
      }
    } catch (e) {
      console.warn('Could not determine pact-broker path, using default');
    }

    try {
      const command = [
        `${pactBrokerPath} record-deployment`,
        `--pacticipant=${participantName}`,
        `--version=${gitCommit}`,
        `--environment=${environment}`,
        `--broker-base-url=${brokerBaseUrl}`,
        `--broker-username=${brokerUsername}`,
        `--broker-password=${brokerPassword}`
      ].join(' ');

      execSync(command, { stdio: 'inherit' });
      console.log(`✅ Deployment of ${participantName} to ${environment} recorded successfully!`);
    } catch (error) {
      console.error(`❌ Failed to record deployment of ${participantName} to ${environment}:`);
      console.error((error as Error).message);
      // Don't exit with error - this is not critical enough to fail deployment
      // but we want to log the error
    }
  }
}