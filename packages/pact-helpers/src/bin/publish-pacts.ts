#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to publish pacts to the pact broker
 * 
 * Usage:
 *   publish-pacts <consumer-name>
 * 
 * Example:
 *   publish-pacts sts-sync-workflow
 */

import { execSync } from 'child_process';
import path from 'path';
import fs from 'fs';
import { Publisher, PublisherOptions } from '@pact-foundation/pact-node';
import { brokerConfig, getCentralPactsDirectory } from '../index';
// Get Git user information
const getGitUserInfo = (): {email: string; username: string} => {
  try {
    const email = execSync('git config user.email').toString().trim();
    const username = execSync('git config user.name').toString().trim();
    return { email, username };
  } catch (error) {
    console.warn('Could not retrieve git user info. Using placeholder values.');
    return { email: 'unknown-email', username: 'unknown-user' };
  }
};

// Get version info based on git state
const getVersionInfo = (): { version: string; isDraft: boolean; username: string } => {
  // Check if there are uncommitted changes
  const status = execSync('git status --porcelain').toString();
  const hasUncommittedChanges = status.trim().length > 0;

  // Get the actual commit hash (used for committed changes)
  const commit = execSync('git rev-parse HEAD').toString().trim();

  // Get user info for draft versions
  const { username } = getGitUserInfo();
  const safeUsername = username.toLowerCase().replace(/\s+/g, '-');

  if (!hasUncommittedChanges) {
    // For committed changes, use the commit hash as version
    return {
      version: commit,
      isDraft: false,
      username: safeUsername
    };
  } else {
    // For drafts, use a consistent name that will be overwritten each time
    // Format: "draft-username" - this means each user has only ONE draft version at a time
    return {
      version: `draft-${safeUsername}`,
      isDraft: true,
      username: safeUsername
    };
  }
};

const getGitBranch = (): string => {
  return execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
};

const publishPacts = async () => {
  const versionInfo = getVersionInfo();
  const gitBranch = getGitBranch();
  
  // Destructure the version info
  const { version, isDraft, username: safeUsername } = versionInfo;
  
  // Get broker details from centralized configuration
  const { url: brokerBaseUrl, username: brokerUsername, password: brokerPassword } = brokerConfig;
  
  if (!brokerBaseUrl || !brokerUsername || !brokerPassword) {
    console.error('Error: Pact broker details not found in configuration!');
    console.error('Make sure PACT_BROKER_URL, PACT_BROKER_USERNAME, and PACT_BROKER_PASSWORD are set in environment variables or .env.test file.');
    process.exit(1);
  }
  
  // Check for consumer argument
  const consumer = process.argv[2];
  
  if (!consumer) {
    console.error('Error: Consumer name is required');
    console.error('Usage: publish-pacts <consumer-name>');
    console.error('Example: publish-pacts sts-sync-workflow');
    process.exit(1);
  }
  
  // Get the central pacts directory for this consumer
  const centralPactsDir = getCentralPactsDirectory(consumer);
  
  // Ensure the directory exists
  if (!fs.existsSync(centralPactsDir)) {
    console.error(`Error: Central pacts directory not found at: ${centralPactsDir}`);
    process.exit(1);
  }
  
  // Check if there are any pact files
  const pactFiles = fs.readdirSync(centralPactsDir).filter(file => file.endsWith('.json'));
  
  if (pactFiles.length === 0) {
    console.warn(`Warning: No pact files found in directory: ${centralPactsDir}`);
    console.warn('Make sure your tests are generating pact files.');
    process.exit(0);
  }
  
  console.log(`Found ${pactFiles.length} pact files in ${centralPactsDir}`);
  
  // Set up publisher options
  const options: PublisherOptions = {
    pactFilesOrDirs: [centralPactsDir],
    pactBroker: brokerBaseUrl,
    pactBrokerUsername: brokerUsername,
    pactBrokerPassword: brokerPassword,
    consumerVersion: version,

    // Add tags - always include branch name
    // For drafts, add both generic 'draft' tag and user-specific draft tag
    tags: isDraft
      ? [gitBranch, 'draft', `${safeUsername}-draft`]
      : [gitBranch],
    branch: gitBranch
  };
  
  try {
    console.log('Publishing pacts to broker...');
    console.log(`Options: ${JSON.stringify(options, (key, value) => 
      key === 'pactBrokerPassword' ? '********' : value, 2)}`);
    
    const publisher = new Publisher(options);
    const results = await publisher.publish();
    
    // Print success message
    console.log('\nPact contract publishing complete!');

    if (isDraft) {
      console.log('\n⚠️  WARNING: This is a DRAFT version with local uncommitted changes! ⚠️');
      console.log(`This draft is tagged with your username: "${safeUsername}-draft"`);
      console.log('This version is for testing purposes only and should not be used for verification in CI/CD pipelines.');
    }

    console.log('Published pact files:');
    results.forEach((file: any) => console.log(`- ${file}`));

  } catch (error) {
    console.error('Error publishing pacts:', error);
    process.exit(1);
  }
};

// Execute the main function
publishPacts().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
