{"name": "@siv/lead-types", "version": "0.0.1", "private": true, "description": "Shared lead type definitions for SIV applications", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "check-types": "tsc --noEmit", "test": "echo 'no tests yet for this package'", "test:watch": "vitest"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "dependencies": {}, "devDependencies": {"@types/node": "^20.17.19", "typescript": "^5.8.3", "vitest": "^3.1.2", "vite": "^6.3.3"}, "publishConfig": {"access": "public"}, "license": "ISC"}