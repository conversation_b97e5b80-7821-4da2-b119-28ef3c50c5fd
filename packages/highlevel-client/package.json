{"name": "@siv/highlevel-client", "version": "0.1.0", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "check-types": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@aws-sdk/client-s3": "^3.777.0", "date-fns": "^4.1.0", "neverthrow": "8.2.0", "pino": "^9.6.0", "@siv/lead-sync-field-helpers": "workspace:*", "@siv/lead-types": "workspace:*"}, "devDependencies": {"@faker-js/faker": "^9.4.0", "@types/node": "^18.0.0", "dotenv-flow": "^4.1.0", "fishery": "^2.2.3", "nock": "^14.0.0", "vite": "6.2.6", "vitest": "^3.0.5", "typescript": "^5.8.3"}}