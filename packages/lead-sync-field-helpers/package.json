{"name": "@siv/lead-sync-field-helpers", "version": "0.0.1", "private": true, "description": "Shared helper functions for formatting lead data for syncing to downstream systems like HighLevel or STS.", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "check-types": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "dependencies": {"neverthrow": "^6.2.1", "date-fns": "^3.6.0", "@siv/lead-types": "workspace:*"}, "devDependencies": {"@faker-js/faker": "^9.4.0", "@types/node": "^18.0.0", "dotenv-flow": "^4.1.0", "fishery": "^2.2.3", "nock": "^14.0.0", "typescript": "^5.8.3", "vite": "6.2.6", "vitest": "^3.0.5"}, "publishConfig": {"access": "public"}, "license": "ISC"}